<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LlamaIndex Agent Chat</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <script src="https://cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div id="app" class="chat-container">
    <h1 class="mb-4">LlamaIndex Agent Chat</h1>
    
    <div class="message-list mb-4">
      <div v-for="(message, index) in messages" :key="index" class="message-container">
        <div :class="message.role === 'user' ? 'user-message' : 'assistant-message'">
          <div v-if="message.role === 'user'" v-text="message.content"></div>
          <div v-else v-html="formatMessage(message.content)"></div>
        </div>
        
        <div v-if="message.citations && message.citations.length > 0" class="sources-section">
          <h6>Sources:</h6>
          <div v-for="(citation, citIndex) in message.citations" :key="citIndex" class="source-item">
            <div class="source-icon">
              <span class="badge bg-secondary">TXT</span>
            </div>
            <div class="source-info">
              <div>{{ citation.title }}</div>
              <div class="small text-muted">
                <span class="citation-number me-2">{{ citIndex + 1 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="isLoading" class="alert alert-info">
      Agent is thinking...
    </div>
    
    <div class="input-group mb-3">
      <input 
        type="text" 
        class="form-control" 
        v-model="userInput" 
        @keyup.enter="sendMessage" 
        placeholder="Ask the agent a question..." 
        :disabled="isLoading"
      >
      <button 
        class="btn btn-primary" 
        @click="sendMessage" 
        :disabled="isLoading || !userInput.trim()"
      >
        Send
      </button>
    </div>
    
    <!-- 引用内容模态框 -->
    <div class="modal fade" id="citationModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Citation Source</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="citation-content" v-html="currentCitationContent"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="app.js"></script>
</body>
</html>