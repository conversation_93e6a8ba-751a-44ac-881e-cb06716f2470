const { createApp, ref, onMounted } = Vue;

createApp({
  setup() {
    const messages = ref([]);
    const userInput = ref("");
    const isLoading = ref(false);
    const currentCitationContent = ref("");
    let citationModal = null;

    // 在组件挂载后初始化模态框
    onMounted(() => {
      citationModal = new bootstrap.Modal(
        document.getElementById("citationModal")
      );

      // 添加事件委托，处理引用点击
      document.addEventListener("click", async (e) => {
        if (e.target.classList.contains("citation-number")) {
          const citationId = e.target.dataset.citationId;
          await loadCitationContent(citationId);
          citationModal.show();
        }
      });
    });

    // 发送消息并处理流式响应
    const sendMessage = async () => {
      if (!userInput.value.trim() || isLoading.value) return;

      // 添加用户消息
      messages.value.push({
        role: "user",
        content: userInput.value,
      });

      const userMessage = userInput.value;
      userInput.value = "";
      isLoading.value = true;

      // 添加一个空的助手消息，用于流式更新
      const assistantMessageIndex = messages.value.length;
      messages.value.push({
        role: "assistant",
        content: "",
        citations: [],
      });

      try {
        // 准备所有历史消息，以保持对话上下文
        const messageHistory = messages.value
          .slice(0, assistantMessageIndex)
          .map((msg) => ({
            role: msg.role,
            content: msg.content,
          }));

        // 使用Agent工作流的聊天端点
        const response = await fetch("/api/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            id: `chat-${Date.now()}`, // 添加必需的id字段
            messages: messageHistory,
          }),
        });

        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let responseText = "";
        let citations = [];
        let buffer = "";

        // 处理流式响应 - 先打印原始数据用于调试
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });

          // 打印原始数据块用于调试
          console.log("=== 原始数据块 ===");
          console.log(chunk);
          console.log("=== 数据块结束 ===");

          // 暂时直接累加所有内容，不做复杂解析
          responseText += chunk;

          // 解析引用信息 - 匹配[citation:id]格式
          const citationMatches = responseText.match(/\[citation:(.*?)\]/g);
          if (citationMatches) {
            citations = [
              ...new Set(
                citationMatches.map((match) => {
                  const id = match.match(/\[citation:(.*?)\]/)[1];
                  // 从ID中提取文件名作为标题
                  const titleMatch = id.match(/([^-]+)-/);
                  const title = titleMatch
                    ? `常见问题类-${titleMatch[1].substring(0, 8)}...`
                    : `Document ${id.substring(0, 8)}...`;

                  return {
                    id,
                    title,
                    content: "Citation content will be loaded when clicked",
                  };
                })
              ),
            ];
          }

          // 更新助手消息
          messages.value[assistantMessageIndex] = {
            role: "assistant",
            content: responseText,
            citations,
          };
        }
      } catch (error) {
        console.error("Error:", error);
        messages.value[assistantMessageIndex].content +=
          "\n\nError: Failed to get response.";
      } finally {
        isLoading.value = false;
      }
    };

    // 格式化消息，处理引用标记
    const formatMessage = (content) => {
      // 替换引用标记为可点击的数字
      let formattedContent = content.replace(
        /\[citation:(.*?)\]/g,
        (match, id) => {
          const citationNumber = getCitationNumber(content, id);
          return `<span class="citation-number" data-citation-id="${id}">[${citationNumber}]</span>`;
        }
      );

      // 使用marked库渲染Markdown
      return marked.parse(formattedContent);
    };

    // 获取引用编号
    const getCitationNumber = (content, targetId) => {
      const citations = content.match(/\[citation:(.*?)\]/g) || [];
      const ids = citations.map((c) => c.match(/\[citation:(.*?)\]/)[1]);
      return ids.indexOf(targetId) + 1;
    };

    // 加载引用内容
    const loadCitationContent = async (citationId) => {
      try {
        currentCitationContent.value = "Loading...";

        // 调用引用内容端点
        const response = await fetch(`/api/citation/${citationId}`);
        if (!response.ok) throw new Error("Failed to load citation");

        const data = await response.json();

        // 格式化引用内容，添加标题
        let formattedContent = "";
        if (data.metadata && data.metadata.file_name) {
          formattedContent += `<h4>${data.metadata.file_name}</h4>`;
        }
        formattedContent += marked.parse(
          data.content || data.text || "内容不可用"
        );

        currentCitationContent.value = formattedContent;
      } catch (error) {
        console.error("Error loading citation:", error);
        currentCitationContent.value = "Failed to load citation content.";
      }
    };

    return {
      messages,
      userInput,
      isLoading,
      currentCitationContent,
      sendMessage,
      formatMessage,
    };
  },
}).mount("#app");
