"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5395],{39412:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});class s{constructor(e,t,s){this.line=void 0,this.column=void 0,this.index=void 0,this.line=e,this.column=t,this.index=s}}class i{constructor(e,t){this.start=void 0,this.end=void 0,this.filename=void 0,this.identifierName=void 0,this.start=e,this.end=t}}function r(e,t){let{line:i,column:r,index:a}=e;return new s(i,r+t,a+t)}let a="BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED",n={ArrayPattern:"array destructuring pattern",AssignmentExpression:"assignment expression",AssignmentPattern:"assignment expression",ArrowFunctionExpression:"arrow function expression",ConditionalExpression:"conditional expression",CatchClause:"catch clause",ForOfStatement:"for-of statement",ForInStatement:"for-in statement",ForStatement:"for-loop",FormalParameters:"function parameter list",Identifier:"identifier",ImportSpecifier:"import specifier",ImportDefaultSpecifier:"import default specifier",ImportNamespaceSpecifier:"import namespace specifier",ObjectPattern:"object destructuring pattern",ParenthesizedExpression:"parenthesized expression",RestElement:"rest element",UpdateExpression:{true:"prefix operation",false:"postfix operation"},VariableDeclarator:"variable declaration",YieldExpression:"yield expression"},o=e=>"UpdateExpression"===e.type?n.UpdateExpression[`${e.prefix}`]:n[e.type],h=new Set(["ArrowFunctionExpression","AssignmentExpression","ConditionalExpression","YieldExpression"]);var l=Object.assign({PipeBodyIsTighter:"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.",PipeTopicRequiresHackPipes:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.',PipeTopicUnbound:"Topic reference is unbound; it must be inside a pipe body.",PipeTopicUnconfiguredToken:({token:e})=>`Invalid topic token ${e}. In order to use ${e} as a topic reference, the pipelineOperator plugin must be configured with { "proposal": "hack", "topicToken": "${e}" }.`,PipeTopicUnused:"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.",PipeUnparenthesizedBody:({type:e})=>`Hack-style pipe body cannot be an unparenthesized ${o({type:e})}; please wrap it in parentheses.`},{PipelineBodyNoArrow:'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized.',PipelineBodySequenceExpression:"Pipeline body may not be a comma-separated sequence expression.",PipelineHeadSequenceExpression:"Pipeline head should not be a comma-separated sequence expression.",PipelineTopicUnused:"Pipeline is in topic style but does not use topic reference.",PrimaryTopicNotAllowed:"Topic reference was used in a lexical context without topic binding.",PrimaryTopicRequiresSmartPipeline:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.'});let p=["message"];function c(e,t,s){Object.defineProperty(e,t,{enumerable:!1,configurable:!0,value:s})}function u(e,t){if(Array.isArray(e))return t=>u(t,e[0]);let i={};for(let r of Object.keys(e)){let a=e[r],n="string"==typeof a?{message:()=>a}:"function"==typeof a?{message:a}:a,{message:o}=n,h=function(e,t){if(null==e)return{};var s={};for(var i in e)if(({}).hasOwnProperty.call(e,i)){if(-1!==t.indexOf(i))continue;s[i]=e[i]}return s}(n,p),l="string"==typeof o?()=>o:o;i[r]=function({toMessage:e,code:t,reasonCode:i,syntaxPlugin:r}){let a="MissingPlugin"===i||"MissingOneOfPlugins"===i;{let e={AccessorCannotDeclareThisParameter:"AccesorCannotDeclareThisParameter",AccessorCannotHaveTypeParameters:"AccesorCannotHaveTypeParameters",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:"ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference",SetAccessorCannotHaveOptionalParameter:"SetAccesorCannotHaveOptionalParameter",SetAccessorCannotHaveRestParameter:"SetAccesorCannotHaveRestParameter",SetAccessorCannotHaveReturnType:"SetAccesorCannotHaveReturnType"};e[i]&&(i=e[i])}return function n(o,h){let l=SyntaxError();return l.code=t,l.reasonCode=i,l.loc=o,l.pos=o.index,l.syntaxPlugin=r,a&&(l.missingPlugin=h.missingPlugin),c(l,"clone",function(e={}){var t;let{line:i,column:r,index:a}=null!=(t=e.loc)?t:o;return n(new s(i,r,a),Object.assign({},h,e.details))}),c(l,"details",h),Object.defineProperty(l,"message",{configurable:!0,get(){let t=`${e(h)} (${o.line}:${o.column})`;return this.message=t,t},set(e){Object.defineProperty(this,"message",{value:e,writable:!0})}}),l}}(Object.assign({code:"BABEL_PARSER_SYNTAX_ERROR",reasonCode:r,toMessage:l},t?{syntaxPlugin:t}:{},h))}return i}let d=Object.assign({},u({ImportMetaOutsideModule:{message:"import.meta may appear only with 'sourceType: \"module\"'",code:a},ImportOutsideModule:{message:"'import' and 'export' may appear only with 'sourceType: \"module\"'",code:a}}),u({AccessorIsGenerator:({kind:e})=>`A ${e}ter cannot be a generator.`,ArgumentsInClass:"'arguments' is only allowed in functions and class methods.",AsyncFunctionInSingleStatementContext:"Async functions can only be declared at the top level or inside a block.",AwaitBindingIdentifier:"Can not use 'await' as identifier inside an async function.",AwaitBindingIdentifierInStaticBlock:"Can not use 'await' as identifier inside a static block.",AwaitExpressionFormalParameter:"'await' is not allowed in async function parameters.",AwaitUsingNotInAsyncContext:"'await using' is only allowed within async functions and at the top levels of modules.",AwaitNotInAsyncContext:"'await' is only allowed within async functions and at the top levels of modules.",BadGetterArity:"A 'get' accessor must not have any formal parameters.",BadSetterArity:"A 'set' accessor must have exactly one formal parameter.",BadSetterRestParameter:"A 'set' accessor function argument must not be a rest parameter.",ConstructorClassField:"Classes may not have a field named 'constructor'.",ConstructorClassPrivateField:"Classes may not have a private field named '#constructor'.",ConstructorIsAccessor:"Class constructor may not be an accessor.",ConstructorIsAsync:"Constructor can't be an async function.",ConstructorIsGenerator:"Constructor can't be a generator.",DeclarationMissingInitializer:({kind:e})=>`Missing initializer in ${e} declaration.`,DecoratorArgumentsOutsideParentheses:"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.",DecoratorBeforeExport:"Decorators must be placed *before* the 'export' keyword. Remove the 'decoratorsBeforeExport: true' option to use the 'export @decorator class {}' syntax.",DecoratorsBeforeAfterExport:"Decorators can be placed *either* before or after the 'export' keyword, but not in both locations at the same time.",DecoratorConstructor:"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?",DecoratorExportClass:"Decorators must be placed *after* the 'export' keyword. Remove the 'decoratorsBeforeExport: false' option to use the '@decorator export class {}' syntax.",DecoratorSemicolon:"Decorators must not be followed by a semicolon.",DecoratorStaticBlock:"Decorators can't be used with a static block.",DeferImportRequiresNamespace:'Only `import defer * as x from "./module"` is valid.',DeletePrivateField:"Deleting a private field is not allowed.",DestructureNamedImport:"ES2015 named imports do not destructure. Use another statement for destructuring after the import.",DuplicateConstructor:"Duplicate constructor in the same class.",DuplicateDefaultExport:"Only one default export allowed per module.",DuplicateExport:({exportName:e})=>`\`${e}\` has already been exported. Exported identifiers must be unique.`,DuplicateProto:"Redefinition of __proto__ property.",DuplicateRegExpFlags:"Duplicate regular expression flag.",DynamicImportPhaseRequiresImportExpressions:({phase:e})=>`'import.${e}(...)' can only be parsed when using the 'createImportExpressions' option.`,ElementAfterRest:"Rest element must be last element.",EscapedCharNotAnIdentifier:"Invalid Unicode escape.",ExportBindingIsString:({localName:e,exportName:t})=>`A string literal cannot be used as an exported binding without \`from\`.
- Did you mean \`export { '${e}' as '${t}' } from 'some-module'\`?`,ExportDefaultFromAsIdentifier:"'from' is not allowed as an identifier after 'export default'.",ForInOfLoopInitializer:({type:e})=>`'${"ForInStatement"===e?"for-in":"for-of"}' loop variable declaration may not have an initializer.`,ForInUsing:"For-in loop may not start with 'using' declaration.",ForOfAsync:"The left-hand side of a for-of loop may not be 'async'.",ForOfLet:"The left-hand side of a for-of loop may not start with 'let'.",GeneratorInSingleStatementContext:"Generators can only be declared at the top level or inside a block.",IllegalBreakContinue:({type:e})=>`Unsyntactic ${"BreakStatement"===e?"break":"continue"}.`,IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list.",IllegalReturn:"'return' outside of function.",ImportAttributesUseAssert:"The `assert` keyword in import attributes is deprecated and it has been replaced by the `with` keyword. You can enable the `deprecatedImportAssert` parser plugin to suppress this error.",ImportBindingIsString:({importName:e})=>`A string literal cannot be used as an imported binding.
- Did you mean \`import { "${e}" as foo }\`?`,ImportCallArity:"`import()` requires exactly one or two arguments.",ImportCallNotNewExpression:"Cannot use new with import(...).",ImportCallSpreadArgument:"`...` is not allowed in `import()`.",ImportJSONBindingNotDefault:"A JSON module can only be imported with `default`.",ImportReflectionHasAssertion:"`import module x` cannot have assertions.",ImportReflectionNotBinding:'Only `import module x from "./module"` is valid.',IncompatibleRegExpUVFlags:"The 'u' and 'v' regular expression flags cannot be enabled at the same time.",InvalidBigIntLiteral:"Invalid BigIntLiteral.",InvalidCodePoint:"Code point out of bounds.",InvalidCoverInitializedName:"Invalid shorthand property initializer.",InvalidDecimal:"Invalid decimal.",InvalidDigit:({radix:e})=>`Expected number in radix ${e}.`,InvalidEscapeSequence:"Bad character escape sequence.",InvalidEscapeSequenceTemplate:"Invalid escape sequence in template.",InvalidEscapedReservedWord:({reservedWord:e})=>`Escape sequence in keyword ${e}.`,InvalidIdentifier:({identifierName:e})=>`Invalid identifier ${e}.`,InvalidLhs:({ancestor:e})=>`Invalid left-hand side in ${o(e)}.`,InvalidLhsBinding:({ancestor:e})=>`Binding invalid left-hand side in ${o(e)}.`,InvalidLhsOptionalChaining:({ancestor:e})=>`Invalid optional chaining in the left-hand side of ${o(e)}.`,InvalidNumber:"Invalid number.",InvalidOrMissingExponent:"Floating-point numbers require a valid exponent after the 'e'.",InvalidOrUnexpectedToken:({unexpected:e})=>`Unexpected character '${e}'.`,InvalidParenthesizedAssignment:"Invalid parenthesized assignment pattern.",InvalidPrivateFieldResolution:({identifierName:e})=>`Private name #${e} is not defined.`,InvalidPropertyBindingPattern:"Binding member expression.",InvalidRecordProperty:"Only properties and spread elements are allowed in record definitions.",InvalidRestAssignmentPattern:"Invalid rest operator's argument.",LabelRedeclaration:({labelName:e})=>`Label '${e}' is already declared.`,LetInLexicalBinding:"'let' is disallowed as a lexically bound name.",LineTerminatorBeforeArrow:"No line break is allowed before '=>'.",MalformedRegExpFlags:"Invalid regular expression flag.",MissingClassName:"A class name is required.",MissingEqInAssignment:"Only '=' operator can be used for specifying default value.",MissingSemicolon:"Missing semicolon.",MissingPlugin:({missingPlugin:e})=>`This experimental syntax requires enabling the parser plugin: ${e.map(e=>JSON.stringify(e)).join(", ")}.`,MissingOneOfPlugins:({missingPlugin:e})=>`This experimental syntax requires enabling one of the following parser plugin(s): ${e.map(e=>JSON.stringify(e)).join(", ")}.`,MissingUnicodeEscape:"Expecting Unicode escape sequence \\uXXXX.",MixingCoalesceWithLogical:"Nullish coalescing operator(??) requires parens when mixing with logical operators.",ModuleAttributeDifferentFromType:"The only accepted module attribute is `type`.",ModuleAttributeInvalidValue:"Only string literals are allowed as module attribute values.",ModuleAttributesWithDuplicateKeys:({key:e})=>`Duplicate key "${e}" is not allowed in module attributes.`,ModuleExportNameHasLoneSurrogate:({surrogateCharCode:e})=>`An export name cannot include a lone surrogate, found '\\u${e.toString(16)}'.`,ModuleExportUndefined:({localName:e})=>`Export '${e}' is not defined.`,MultipleDefaultsInSwitch:"Multiple default clauses.",NewlineAfterThrow:"Illegal newline after throw.",NoCatchOrFinally:"Missing catch or finally clause.",NumberIdentifier:"Identifier directly after number.",NumericSeparatorInEscapeSequence:"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.",ObsoleteAwaitStar:"'await*' has been removed from the async functions proposal. Use Promise.all() instead.",OptionalChainingNoNew:"Constructors in/after an Optional Chain are not allowed.",OptionalChainingNoTemplate:"Tagged Template Literals are not allowed in optionalChain.",OverrideOnConstructor:"'override' modifier cannot appear on a constructor declaration.",ParamDupe:"Argument name clash.",PatternHasAccessor:"Object pattern can't contain getter or setter.",PatternHasMethod:"Object pattern can't contain methods.",PrivateInExpectedIn:({identifierName:e})=>`Private names are only allowed in property accesses (\`obj.#${e}\`) or in \`in\` expressions (\`#${e} in obj\`).`,PrivateNameRedeclaration:({identifierName:e})=>`Duplicate private name #${e}.`,RecordExpressionBarIncorrectEndSyntaxType:"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionBarIncorrectStartSyntaxType:"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionHashIncorrectStartSyntaxType:"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",RecordNoProto:"'__proto__' is not allowed in Record expressions.",RestTrailingComma:"Unexpected trailing comma after rest element.",SloppyFunction:"In non-strict mode code, functions can only be declared at top level or inside a block.",SloppyFunctionAnnexB:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.",SourcePhaseImportRequiresDefault:'Only `import source x from "./module"` is valid.',StaticPrototype:"Classes may not have static property named prototype.",SuperNotAllowed:"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?",SuperPrivateField:"Private fields can't be accessed on super.",TrailingDecorator:"Decorators must be attached to a class element.",TupleExpressionBarIncorrectEndSyntaxType:"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionBarIncorrectStartSyntaxType:"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionHashIncorrectStartSyntaxType:"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",UnexpectedArgumentPlaceholder:"Unexpected argument placeholder.",UnexpectedAwaitAfterPipelineBody:'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal.',UnexpectedDigitAfterHash:"Unexpected digit after hash token.",UnexpectedImportExport:"'import' and 'export' may only appear at the top level.",UnexpectedKeyword:({keyword:e})=>`Unexpected keyword '${e}'.`,UnexpectedLeadingDecorator:"Leading decorators must be attached to a class declaration.",UnexpectedLexicalDeclaration:"Lexical declaration cannot appear in a single-statement context.",UnexpectedNewTarget:"`new.target` can only be used in functions or class properties.",UnexpectedNumericSeparator:"A numeric separator is only allowed between two digits.",UnexpectedPrivateField:"Unexpected private name.",UnexpectedReservedWord:({reservedWord:e})=>`Unexpected reserved word '${e}'.`,UnexpectedSuper:"'super' is only allowed in object methods and classes.",UnexpectedToken:({expected:e,unexpected:t})=>`Unexpected token${t?` '${t}'.`:""}${e?`, expected "${e}"`:""}`,UnexpectedTokenUnaryExponentiation:"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.",UnexpectedUsingDeclaration:"Using declaration cannot appear in the top level when source type is `script`.",UnsupportedBind:"Binding should be performed on object property.",UnsupportedDecoratorExport:"A decorated export must export a class declaration.",UnsupportedDefaultExport:"Only expressions, functions or classes are allowed as the `default` export.",UnsupportedImport:"`import` can only be used in `import()` or `import.meta`.",UnsupportedMetaProperty:({target:e,onlyValidPropertyName:t})=>`The only valid meta property for ${e} is ${e}.${t}.`,UnsupportedParameterDecorator:"Decorators cannot be used to decorate parameters.",UnsupportedPropertyDecorator:"Decorators cannot be used to decorate object literal properties.",UnsupportedSuper:"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).",UnterminatedComment:"Unterminated comment.",UnterminatedRegExp:"Unterminated regular expression.",UnterminatedString:"Unterminated string constant.",UnterminatedTemplate:"Unterminated template.",UsingDeclarationExport:"Using declaration cannot be exported.",UsingDeclarationHasBindingPattern:"Using declaration cannot have destructuring patterns.",VarRedeclaration:({identifierName:e})=>`Identifier '${e}' has already been declared.`,YieldBindingIdentifier:"Can not use 'yield' as identifier inside a generator.",YieldInParameter:"Yield expression is not allowed in formal parameters.",YieldNotInGeneratorFunction:"'yield' is only allowed within generator functions.",ZeroDigitNumericSeparator:"Numeric separator can not be used after leading 0."}),u({StrictDelete:"Deleting local variable in strict mode.",StrictEvalArguments:({referenceName:e})=>`Assigning to '${e}' in strict mode.`,StrictEvalArgumentsBinding:({bindingName:e})=>`Binding '${e}' in strict mode.`,StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block.",StrictNumericEscape:"The only valid numeric escape in strict mode is '\\0'.",StrictOctalLiteral:"Legacy octal literals are not allowed in strict mode.",StrictWith:"'with' in strict mode."}),u`pipelineOperator`(l)),{defineProperty:m}=Object,f=(e,t)=>{e&&m(e,t,{enumerable:!1,value:e[t]})};function y(e){return f(e.loc.start,"index"),f(e.loc.end,"index"),e}class x{constructor(e,t){this.token=void 0,this.preserveSpace=void 0,this.token=e,this.preserveSpace=!!t}}let P={brace:new x("{"),j_oTag:new x("<tag"),j_cTag:new x("</tag"),j_expr:new x("<tag>...</tag>",!0)};P.template=new x("`",!0);class g{constructor(e,t={}){this.label=void 0,this.keyword=void 0,this.beforeExpr=void 0,this.startsExpr=void 0,this.rightAssociative=void 0,this.isLoop=void 0,this.isAssign=void 0,this.prefix=void 0,this.postfix=void 0,this.binop=void 0,this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.rightAssociative=!!t.rightAssociative,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=null!=t.binop?t.binop:null,this.updateContext=null}}let T=new Map;function b(e,t={}){t.keyword=e;let s=k(e,t);return T.set(e,s),s}function A(e,t){return k(e,{beforeExpr:!0,binop:t})}let E=-1,S=[],I=[],C=[],w=[],N=[],v=[];function k(e,t={}){var s,i,r,a;return++E,I.push(e),C.push(null!=(s=t.binop)?s:-1),w.push(null!=(i=t.beforeExpr)&&i),N.push(null!=(r=t.startsExpr)&&r),v.push(null!=(a=t.prefix)&&a),S.push(new g(e,t)),E}function L(e,t={}){var s,i,r,a;return++E,T.set(e,E),I.push(e),C.push(null!=(s=t.binop)?s:-1),w.push(null!=(i=t.beforeExpr)&&i),N.push(null!=(r=t.startsExpr)&&r),v.push(null!=(a=t.prefix)&&a),S.push(new g("name",t)),E}let M={bracketL:k("[",{beforeExpr:!0,startsExpr:!0}),bracketHashL:k("#[",{beforeExpr:!0,startsExpr:!0}),bracketBarL:k("[|",{beforeExpr:!0,startsExpr:!0}),bracketR:k("]"),bracketBarR:k("|]"),braceL:k("{",{beforeExpr:!0,startsExpr:!0}),braceBarL:k("{|",{beforeExpr:!0,startsExpr:!0}),braceHashL:k("#{",{beforeExpr:!0,startsExpr:!0}),braceR:k("}"),braceBarR:k("|}"),parenL:k("(",{beforeExpr:!0,startsExpr:!0}),parenR:k(")"),comma:k(",",{beforeExpr:!0}),semi:k(";",{beforeExpr:!0}),colon:k(":",{beforeExpr:!0}),doubleColon:k("::",{beforeExpr:!0}),dot:k("."),question:k("?",{beforeExpr:!0}),questionDot:k("?."),arrow:k("=>",{beforeExpr:!0}),template:k("template"),ellipsis:k("...",{beforeExpr:!0}),backQuote:k("`",{startsExpr:!0}),dollarBraceL:k("${",{beforeExpr:!0,startsExpr:!0}),templateTail:k("...`",{startsExpr:!0}),templateNonTail:k("...${",{beforeExpr:!0,startsExpr:!0}),at:k("@"),hash:k("#",{startsExpr:!0}),interpreterDirective:k("#!..."),eq:k("=",{beforeExpr:!0,isAssign:!0}),assign:k("_=",{beforeExpr:!0,isAssign:!0}),slashAssign:k("_=",{beforeExpr:!0,isAssign:!0}),xorAssign:k("_=",{beforeExpr:!0,isAssign:!0}),moduloAssign:k("_=",{beforeExpr:!0,isAssign:!0}),incDec:k("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),bang:k("!",{beforeExpr:!0,prefix:!0,startsExpr:!0}),tilde:k("~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),doubleCaret:k("^^",{startsExpr:!0}),doubleAt:k("@@",{startsExpr:!0}),pipeline:A("|>",0),nullishCoalescing:A("??",1),logicalOR:A("||",1),logicalAND:A("&&",2),bitwiseOR:A("|",3),bitwiseXOR:A("^",4),bitwiseAND:A("&",5),equality:A("==/!=/===/!==",6),lt:A("</>/<=/>=",7),gt:A("</>/<=/>=",7),relational:A("</>/<=/>=",7),bitShift:A("<</>>/>>>",8),bitShiftL:A("<</>>/>>>",8),bitShiftR:A("<</>>/>>>",8),plusMin:k("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:k("%",{binop:10,startsExpr:!0}),star:k("*",{binop:10}),slash:A("/",10),exponent:k("**",{beforeExpr:!0,binop:11,rightAssociative:!0}),_in:b("in",{beforeExpr:!0,binop:7}),_instanceof:b("instanceof",{beforeExpr:!0,binop:7}),_break:b("break"),_case:b("case",{beforeExpr:!0}),_catch:b("catch"),_continue:b("continue"),_debugger:b("debugger"),_default:b("default",{beforeExpr:!0}),_else:b("else",{beforeExpr:!0}),_finally:b("finally"),_function:b("function",{startsExpr:!0}),_if:b("if"),_return:b("return",{beforeExpr:!0}),_switch:b("switch"),_throw:b("throw",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_try:b("try"),_var:b("var"),_const:b("const"),_with:b("with"),_new:b("new",{beforeExpr:!0,startsExpr:!0}),_this:b("this",{startsExpr:!0}),_super:b("super",{startsExpr:!0}),_class:b("class",{startsExpr:!0}),_extends:b("extends",{beforeExpr:!0}),_export:b("export"),_import:b("import",{startsExpr:!0}),_null:b("null",{startsExpr:!0}),_true:b("true",{startsExpr:!0}),_false:b("false",{startsExpr:!0}),_typeof:b("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:b("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:b("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_do:b("do",{isLoop:!0,beforeExpr:!0}),_for:b("for",{isLoop:!0}),_while:b("while",{isLoop:!0}),_as:L("as",{startsExpr:!0}),_assert:L("assert",{startsExpr:!0}),_async:L("async",{startsExpr:!0}),_await:L("await",{startsExpr:!0}),_defer:L("defer",{startsExpr:!0}),_from:L("from",{startsExpr:!0}),_get:L("get",{startsExpr:!0}),_let:L("let",{startsExpr:!0}),_meta:L("meta",{startsExpr:!0}),_of:L("of",{startsExpr:!0}),_sent:L("sent",{startsExpr:!0}),_set:L("set",{startsExpr:!0}),_source:L("source",{startsExpr:!0}),_static:L("static",{startsExpr:!0}),_using:L("using",{startsExpr:!0}),_yield:L("yield",{startsExpr:!0}),_asserts:L("asserts",{startsExpr:!0}),_checks:L("checks",{startsExpr:!0}),_exports:L("exports",{startsExpr:!0}),_global:L("global",{startsExpr:!0}),_implements:L("implements",{startsExpr:!0}),_intrinsic:L("intrinsic",{startsExpr:!0}),_infer:L("infer",{startsExpr:!0}),_is:L("is",{startsExpr:!0}),_mixins:L("mixins",{startsExpr:!0}),_proto:L("proto",{startsExpr:!0}),_require:L("require",{startsExpr:!0}),_satisfies:L("satisfies",{startsExpr:!0}),_keyof:L("keyof",{startsExpr:!0}),_readonly:L("readonly",{startsExpr:!0}),_unique:L("unique",{startsExpr:!0}),_abstract:L("abstract",{startsExpr:!0}),_declare:L("declare",{startsExpr:!0}),_enum:L("enum",{startsExpr:!0}),_module:L("module",{startsExpr:!0}),_namespace:L("namespace",{startsExpr:!0}),_interface:L("interface",{startsExpr:!0}),_type:L("type",{startsExpr:!0}),_opaque:L("opaque",{startsExpr:!0}),name:k("name",{startsExpr:!0}),placeholder:k("%%",{startsExpr:!0}),string:k("string",{startsExpr:!0}),num:k("num",{startsExpr:!0}),bigint:k("bigint",{startsExpr:!0}),decimal:k("decimal",{startsExpr:!0}),regexp:k("regexp",{startsExpr:!0}),privateName:k("#name",{startsExpr:!0}),eof:k("eof"),jsxName:k("jsxName"),jsxText:k("jsxText",{beforeExpr:!0}),jsxTagStart:k("jsxTagStart",{startsExpr:!0}),jsxTagEnd:k("jsxTagEnd")};function D(e){return e>=93&&e<=133}function O(e){return e>=58&&e<=133}function F(e){return e>=58&&e<=137}function B(e){return e>=129&&e<=131}function R(e){return e>=58&&e<=92}function U(e){return e>=24&&e<=25}S[8].updateContext=e=>{e.pop()},S[5].updateContext=S[7].updateContext=S[23].updateContext=e=>{e.push(P.brace)},S[22].updateContext=e=>{e[e.length-1]===P.template?e.pop():e.push(P.template)},S[143].updateContext=e=>{e.push(P.j_expr,P.j_oTag)};let j="\xaa\xb5\xba\xc0-\xd6\xd8-\xf6\xf8-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲊᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟍꟐꟑꟓꟕ-Ƛꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",_="\xb7̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ࢗ-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ೳഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-໎໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‌‍‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯・꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿･",H=RegExp("["+j+"]"),z=RegExp("["+j+_+"]");j=_=null;let V=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],q=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function $(e,t){let s=65536;for(let i=0,r=t.length;i<r&&!((s+=t[i])>e);i+=2)if((s+=t[i+1])>=e)return!0;return!1}function K(e){return e<65?36===e:e<=90||(e<97?95===e:e<=122||(e<=65535?e>=170&&H.test(String.fromCharCode(e)):$(e,V)))}function J(e){return e<48?36===e:e<58||!(e<65)&&(e<=90||(e<97?95===e:e<=122||(e<=65535?e>=170&&z.test(String.fromCharCode(e)):$(e,V)||$(e,q))))}let W={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},X=new Set(W.keyword),G=new Set(W.strict),Y=new Set(W.strictBind);function Q(e,t){return t&&"await"===e||"enum"===e}function Z(e,t){return Q(e,t)||G.has(e)}function ee(e,t){return Z(e,t)||Y.has(e)}let et=new Set(["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete","implements","interface","let","package","private","protected","public","static","yield","eval","arguments","enum","await"]);class es{constructor(e){this.flags=0,this.names=new Map,this.firstLexicalName="",this.flags=e}}class ei{constructor(e,t){this.parser=void 0,this.scopeStack=[],this.inModule=void 0,this.undefinedExports=new Map,this.parser=e,this.inModule=t}get inTopLevel(){return(1&this.currentScope().flags)>0}get inFunction(){return(2&this.currentVarScopeFlags())>0}get allowSuper(){return(16&this.currentThisScopeFlags())>0}get allowDirectSuper(){return(32&this.currentThisScopeFlags())>0}get inClass(){return(64&this.currentThisScopeFlags())>0}get inClassAndNotInNonArrowFunction(){let e=this.currentThisScopeFlags();return(64&e)>0&&(2&e)==0}get inStaticBlock(){for(let e=this.scopeStack.length-1;;e--){let{flags:t}=this.scopeStack[e];if(128&t)return!0;if(451&t)return!1}}get inNonArrowFunction(){return(2&this.currentThisScopeFlags())>0}get treatFunctionsAsVar(){return this.treatFunctionsAsVarInScope(this.currentScope())}createScope(e){return new es(e)}enter(e){this.scopeStack.push(this.createScope(e))}exit(){return this.scopeStack.pop().flags}treatFunctionsAsVarInScope(e){return!!(130&e.flags||!this.parser.inModule&&1&e.flags)}declareName(e,t,s){let i=this.currentScope();if(8&t||16&t){this.checkRedeclarationInScope(i,e,t,s);let r=i.names.get(e)||0;16&t?r|=4:(i.firstLexicalName||(i.firstLexicalName=e),r|=2),i.names.set(e,r),8&t&&this.maybeExportDefined(i,e)}else if(4&t)for(let r=this.scopeStack.length-1;r>=0&&(i=this.scopeStack[r],this.checkRedeclarationInScope(i,e,t,s),i.names.set(e,1|(i.names.get(e)||0)),this.maybeExportDefined(i,e),!(387&i.flags));--r);this.parser.inModule&&1&i.flags&&this.undefinedExports.delete(e)}maybeExportDefined(e,t){this.parser.inModule&&1&e.flags&&this.undefinedExports.delete(t)}checkRedeclarationInScope(e,t,s,i){this.isRedeclaredInScope(e,t,s)&&this.parser.raise(d.VarRedeclaration,i,{identifierName:t})}isRedeclaredInScope(e,t,s){if(!(1&s))return!1;if(8&s)return e.names.has(t);let i=e.names.get(t);return 16&s?(2&i)>0||!this.treatFunctionsAsVarInScope(e)&&(1&i)>0:(2&i)>0&&!(8&e.flags&&e.firstLexicalName===t)||!this.treatFunctionsAsVarInScope(e)&&(4&i)>0}checkLocalExport(e){let{name:t}=e;this.scopeStack[0].names.has(t)||this.undefinedExports.set(t,e.loc.start)}currentScope(){return this.scopeStack[this.scopeStack.length-1]}currentVarScopeFlags(){for(let e=this.scopeStack.length-1;;e--){let{flags:t}=this.scopeStack[e];if(387&t)return t}}currentThisScopeFlags(){for(let e=this.scopeStack.length-1;;e--){let{flags:t}=this.scopeStack[e];if(451&t&&!(4&t))return t}}}class er extends es{constructor(...e){super(...e),this.declareFunctions=new Set}}class ea extends ei{createScope(e){return new er(e)}declareName(e,t,s){let i=this.currentScope();if(2048&t){this.checkRedeclarationInScope(i,e,t,s),this.maybeExportDefined(i,e),i.declareFunctions.add(e);return}super.declareName(e,t,s)}isRedeclaredInScope(e,t,s){if(super.isRedeclaredInScope(e,t,s))return!0;if(2048&s&&!e.declareFunctions.has(t)){let s=e.names.get(t);return(4&s)>0||(2&s)>0}return!1}checkLocalExport(e){this.scopeStack[0].declareFunctions.has(e.name)||super.checkLocalExport(e)}}class en{constructor(){this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}sourceToOffsetPos(e){return e+this.startIndex}offsetToSourcePos(e){return e-this.startIndex}hasPlugin(e){if("string"==typeof e)return this.plugins.has(e);{let[t,s]=e;if(!this.hasPlugin(t))return!1;let i=this.plugins.get(t);for(let e of Object.keys(s))if((null==i?void 0:i[e])!==s[e])return!1;return!0}}getPluginOption(e,t){var s;return null==(s=this.plugins.get(e))?void 0:s[t]}}function eo(e,t){void 0===e.trailingComments?e.trailingComments=t:e.trailingComments.unshift(...t)}function eh(e,t){void 0===e.innerComments?e.innerComments=t:e.innerComments.unshift(...t)}function el(e,t,s){let i=null,r=t.length;for(;null===i&&r>0;)i=t[--r];null===i||i.start>s.start?eh(e,s.comments):eo(i,s.comments)}class ep extends en{addComment(e){this.filename&&(e.loc.filename=this.filename);let{commentsLen:t}=this.state;this.comments.length!==t&&(this.comments.length=t),this.comments.push(e),this.state.commentsLen++}processComment(e){let{commentStack:t}=this.state,s=t.length;if(0===s)return;let i=s-1,r=t[i];r.start===e.end&&(r.leadingNode=e,i--);let{start:a}=e;for(;i>=0;i--){let s=t[i],r=s.end;if(r>a)s.containingNode=e,this.finalizeComment(s),t.splice(i,1);else{r===a&&(s.trailingNode=e);break}}}finalizeComment(e){let{comments:t}=e;if(null!==e.leadingNode||null!==e.trailingNode){var s;null!==e.leadingNode&&eo(e.leadingNode,t),null!==e.trailingNode&&(s=e.trailingNode,void 0===s.leadingComments?s.leadingComments=t:s.leadingComments.unshift(...t))}else{let{containingNode:s,start:i}=e;if(44===this.input.charCodeAt(this.offsetToSourcePos(i)-1))switch(s.type){case"ObjectExpression":case"ObjectPattern":case"RecordExpression":el(s,s.properties,e);break;case"CallExpression":case"OptionalCallExpression":el(s,s.arguments,e);break;case"FunctionDeclaration":case"FunctionExpression":case"ArrowFunctionExpression":case"ObjectMethod":case"ClassMethod":case"ClassPrivateMethod":el(s,s.params,e);break;case"ArrayExpression":case"ArrayPattern":case"TupleExpression":el(s,s.elements,e);break;case"ExportNamedDeclaration":case"ImportDeclaration":el(s,s.specifiers,e);break;case"TSEnumDeclaration":case"TSEnumBody":el(s,s.members,e);break;default:eh(s,t)}else eh(s,t)}}finalizeRemainingComments(){let{commentStack:e}=this.state;for(let t=e.length-1;t>=0;t--)this.finalizeComment(e[t]);this.state.commentStack=[]}resetPreviousNodeTrailingComments(e){let{commentStack:t}=this.state,{length:s}=t;if(0===s)return;let i=t[s-1];i.leadingNode===e&&(i.leadingNode=null)}resetPreviousIdentifierLeadingComments(e){let{commentStack:t}=this.state,{length:s}=t;0!==s&&(t[s-1].trailingNode===e?t[s-1].trailingNode=null:s>=2&&t[s-2].trailingNode===e&&(t[s-2].trailingNode=null))}takeSurroundingComments(e,t,s){let{commentStack:i}=this.state,r=i.length;if(0===r)return;let a=r-1;for(;a>=0;a--){let r=i[a],n=r.end;if(r.start===s)r.leadingNode=e;else if(n===t)r.trailingNode=e;else if(n<t)break}}}let ec=RegExp(/\r\n|[\r\n\u2028\u2029]/.source,"g");function eu(e){switch(e){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}function ed(e,t,s){for(let i=t;i<s;i++)if(eu(e.charCodeAt(i)))return!0;return!1}let em=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,ef=/(?:[^\S\n\r\u2028\u2029]|\/\/.*|\/\*.*?\*\/)*/g;class ey{constructor(){this.flags=1024,this.startIndex=void 0,this.curLine=void 0,this.lineStart=void 0,this.startLoc=void 0,this.endLoc=void 0,this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.labels=[],this.commentsLen=0,this.commentStack=[],this.pos=0,this.type=140,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.context=[P.brace],this.firstInvalidTemplateEscapePos=null,this.strictErrors=new Map,this.tokensLength=0}get strict(){return(1&this.flags)>0}set strict(e){e?this.flags|=1:this.flags&=-2}init({strictMode:e,sourceType:t,startIndex:i,startLine:r,startColumn:a}){this.strict=!1!==e&&(!0===e||"module"===t),this.startIndex=i,this.curLine=r,this.lineStart=-a,this.startLoc=this.endLoc=new s(r,a,i)}get maybeInArrowParameters(){return(2&this.flags)>0}set maybeInArrowParameters(e){e?this.flags|=2:this.flags&=-3}get inType(){return(4&this.flags)>0}set inType(e){e?this.flags|=4:this.flags&=-5}get noAnonFunctionType(){return(8&this.flags)>0}set noAnonFunctionType(e){e?this.flags|=8:this.flags&=-9}get hasFlowComment(){return(16&this.flags)>0}set hasFlowComment(e){e?this.flags|=16:this.flags&=-17}get isAmbientContext(){return(32&this.flags)>0}set isAmbientContext(e){e?this.flags|=32:this.flags&=-33}get inAbstractClass(){return(64&this.flags)>0}set inAbstractClass(e){e?this.flags|=64:this.flags&=-65}get inDisallowConditionalTypesContext(){return(128&this.flags)>0}set inDisallowConditionalTypesContext(e){e?this.flags|=128:this.flags&=-129}get soloAwait(){return(256&this.flags)>0}set soloAwait(e){e?this.flags|=256:this.flags&=-257}get inFSharpPipelineDirectBody(){return(512&this.flags)>0}set inFSharpPipelineDirectBody(e){e?this.flags|=512:this.flags&=-513}get canStartJSXElement(){return(1024&this.flags)>0}set canStartJSXElement(e){e?this.flags|=1024:this.flags&=-1025}get containsEsc(){return(2048&this.flags)>0}set containsEsc(e){e?this.flags|=2048:this.flags&=-2049}get hasTopLevelAwait(){return(4096&this.flags)>0}set hasTopLevelAwait(e){e?this.flags|=4096:this.flags&=-4097}curPosition(){return new s(this.curLine,this.pos-this.lineStart,this.pos+this.startIndex)}clone(){let e=new ey;return e.flags=this.flags,e.startIndex=this.startIndex,e.curLine=this.curLine,e.lineStart=this.lineStart,e.startLoc=this.startLoc,e.endLoc=this.endLoc,e.errors=this.errors.slice(),e.potentialArrowAt=this.potentialArrowAt,e.noArrowAt=this.noArrowAt.slice(),e.noArrowParamsConversionAt=this.noArrowParamsConversionAt.slice(),e.topicContext=this.topicContext,e.labels=this.labels.slice(),e.commentsLen=this.commentsLen,e.commentStack=this.commentStack.slice(),e.pos=this.pos,e.type=this.type,e.value=this.value,e.start=this.start,e.end=this.end,e.lastTokEndLoc=this.lastTokEndLoc,e.lastTokStartLoc=this.lastTokStartLoc,e.context=this.context.slice(),e.firstInvalidTemplateEscapePos=this.firstInvalidTemplateEscapePos,e.strictErrors=this.strictErrors,e.tokensLength=this.tokensLength,e}}let ex={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])},eP={bin:e=>48===e||49===e,oct:e=>e>=48&&e<=55,dec:e=>e>=48&&e<=57,hex:e=>e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102};function eg(e,t,s,i,r,a){let n=s,o=i,h=r,l="",p=null,c=s,{length:u}=t;for(;;){var d,m,f,y;if(s>=u){a.unterminated(n,o,h),l+=t.slice(c,s);break}let x=t.charCodeAt(s);if(d=e,m=x,f=t,y=s,"template"===d?96===m||36===m&&123===f.charCodeAt(y+1):m===("double"===d?34:39)){l+=t.slice(c,s);break}if(92===x){l+=t.slice(c,s);let n=function(e,t,s,i,r,a){let n=!r;t++;let o=e=>({pos:t,ch:e,lineStart:s,curLine:i}),h=e.charCodeAt(t++);switch(h){case 110:return o("\n");case 114:return o("\r");case 120:{let r;return{code:r,pos:t}=eT(e,t,s,i,2,!1,n,a),o(null===r?null:String.fromCharCode(r))}case 117:{let r;return{code:r,pos:t}=eA(e,t,s,i,n,a),o(null===r?null:String.fromCodePoint(r))}case 116:return o("	");case 98:return o("\b");case 118:return o("\v");case 102:return o("\f");case 13:10===e.charCodeAt(t)&&++t;case 10:s=t,++i;case 8232:case 8233:return o("");case 56:case 57:if(r)return o(null);a.strictNumericEscape(t-1,s,i);default:if(h>=48&&h<=55){let n=t-1,h=/^[0-7]+/.exec(e.slice(n,t+2))[0],l=parseInt(h,8);l>255&&(l=parseInt(h=h.slice(0,-1),8)),t+=h.length-1;let p=e.charCodeAt(t);if("0"!==h||56===p||57===p)if(r)return o(null);else a.strictNumericEscape(n,s,i);return o(String.fromCharCode(l))}return o(String.fromCharCode(h))}}(t,s,i,r,"template"===e,a);null!==n.ch||p?l+=n.ch:p={pos:s,lineStart:i,curLine:r},({pos:s,lineStart:i,curLine:r}=n),c=s}else 8232===x||8233===x?(++s,++r,i=s):10===x||13===x?"template"===e?(l+=t.slice(c,s)+"\n",++s,13===x&&10===t.charCodeAt(s)&&++s,++r,c=i=s):a.unterminated(n,o,h):++s}return{pos:s,str:l,firstInvalidLoc:p,lineStart:i,curLine:r,containsInvalid:!!p}}function eT(e,t,s,i,r,a,n,o){let h,l=t;return{n:h,pos:t}=eb(e,t,s,i,16,r,a,!1,o,!n),null===h&&(n?o.invalidEscapeSequence(l,s,i):t=l-1),{code:h,pos:t}}function eb(e,t,s,i,r,a,n,o,h,l){let p=t,c=16===r?ex.hex:ex.decBinOct,u=16===r?eP.hex:10===r?eP.dec:8===r?eP.oct:eP.bin,d=!1,m=0;for(let p=0,f=null==a?1/0:a;p<f;++p){let a,p=e.charCodeAt(t);if(95===p&&"bail"!==o){let r=e.charCodeAt(t-1),a=e.charCodeAt(t+1);if(o){if(Number.isNaN(a)||!u(a)||c.has(r)||c.has(a)){if(l)return{n:null,pos:t};h.unexpectedNumericSeparator(t,s,i)}}else{if(l)return{n:null,pos:t};h.numericSeparatorInEscapeSequence(t,s,i)}++t;continue}if(p>=97)a=p-97+10;else if(p>=65)a=p-65+10;else a=p>=48&&p<=57?p-48:1/0;if(a>=r)if(a<=9&&l)return{n:null,pos:t};else if(a<=9&&h.invalidDigit(t,s,i,r))a=0;else if(n)a=0,d=!0;else break;++t,m=m*r+a}return t===p||null!=a&&t-p!==a||d?{n:null,pos:t}:{n:m,pos:t}}function eA(e,t,s,i,r,a){let n;if(123===e.charCodeAt(t)){if(++t,{code:n,pos:t}=eT(e,t,s,i,e.indexOf("}",t)-t,!0,r,a),++t,null!==n&&n>1114111)if(!r)return{code:null,pos:t};else a.invalidCodePoint(t,s,i)}else({code:n,pos:t}=eT(e,t,s,i,4,!1,r,a));return{code:n,pos:t}}function eE(e,t,i){return new s(i,e-t,e)}let eS=new Set([103,109,115,105,121,117,100,118]);class eI{constructor(e){let t=e.startIndex||0;this.type=e.type,this.value=e.value,this.start=t+e.start,this.end=t+e.end,this.loc=new i(e.startLoc,e.endLoc)}}class eC extends ep{constructor(e,t){super(),this.isLookahead=void 0,this.tokens=[],this.errorHandlers_readInt={invalidDigit:(e,t,s,i)=>!!(2048&this.optionFlags)&&(this.raise(d.InvalidDigit,eE(e,t,s),{radix:i}),!0),numericSeparatorInEscapeSequence:this.errorBuilder(d.NumericSeparatorInEscapeSequence),unexpectedNumericSeparator:this.errorBuilder(d.UnexpectedNumericSeparator)},this.errorHandlers_readCodePoint=Object.assign({},this.errorHandlers_readInt,{invalidEscapeSequence:this.errorBuilder(d.InvalidEscapeSequence),invalidCodePoint:this.errorBuilder(d.InvalidCodePoint)}),this.errorHandlers_readStringContents_string=Object.assign({},this.errorHandlers_readCodePoint,{strictNumericEscape:(e,t,s)=>{this.recordStrictModeErrors(d.StrictNumericEscape,eE(e,t,s))},unterminated:(e,t,s)=>{throw this.raise(d.UnterminatedString,eE(e-1,t,s))}}),this.errorHandlers_readStringContents_template=Object.assign({},this.errorHandlers_readCodePoint,{strictNumericEscape:this.errorBuilder(d.StrictNumericEscape),unterminated:(e,t,s)=>{throw this.raise(d.UnterminatedTemplate,eE(e,t,s))}}),this.state=new ey,this.state.init(e),this.input=t,this.length=t.length,this.comments=[],this.isLookahead=!1}pushToken(e){this.tokens.length=this.state.tokensLength,this.tokens.push(e),++this.state.tokensLength}next(){this.checkKeywordEscapes(),256&this.optionFlags&&this.pushToken(new eI(this.state)),this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}eat(e){return!!this.match(e)&&(this.next(),!0)}match(e){return this.state.type===e}createLookaheadState(e){return{pos:e.pos,value:null,type:e.type,start:e.start,end:e.end,context:[this.curContext()],inType:e.inType,startLoc:e.startLoc,lastTokEndLoc:e.lastTokEndLoc,curLine:e.curLine,lineStart:e.lineStart,curPosition:e.curPosition}}lookahead(){let e=this.state;this.state=this.createLookaheadState(e),this.isLookahead=!0,this.nextToken(),this.isLookahead=!1;let t=this.state;return this.state=e,t}nextTokenStart(){return this.nextTokenStartSince(this.state.pos)}nextTokenStartSince(e){return em.lastIndex=e,em.test(this.input)?em.lastIndex:e}lookaheadCharCode(){return this.input.charCodeAt(this.nextTokenStart())}nextTokenInLineStart(){return this.nextTokenInLineStartSince(this.state.pos)}nextTokenInLineStartSince(e){return ef.lastIndex=e,ef.test(this.input)?ef.lastIndex:e}lookaheadInLineCharCode(){return this.input.charCodeAt(this.nextTokenInLineStart())}codePointAtPos(e){let t=this.input.charCodeAt(e);if((64512&t)==55296&&++e<this.input.length){let s=this.input.charCodeAt(e);(64512&s)==56320&&(t=65536+((1023&t)<<10)+(1023&s))}return t}setStrict(e){this.state.strict=e,e&&(this.state.strictErrors.forEach(([e,t])=>this.raise(e,t)),this.state.strictErrors.clear())}curContext(){return this.state.context[this.state.context.length-1]}nextToken(){if(this.skipSpace(),this.state.start=this.state.pos,this.isLookahead||(this.state.startLoc=this.state.curPosition()),this.state.pos>=this.length)return void this.finishToken(140);this.getTokenFromCode(this.codePointAtPos(this.state.pos))}skipBlockComment(e){let t;this.isLookahead||(t=this.state.curPosition());let s=this.state.pos,r=this.input.indexOf(e,s+2);if(-1===r)throw this.raise(d.UnterminatedComment,this.state.curPosition());for(this.state.pos=r+e.length,ec.lastIndex=s+2;ec.test(this.input)&&ec.lastIndex<=r;)++this.state.curLine,this.state.lineStart=ec.lastIndex;if(this.isLookahead)return;let a={type:"CommentBlock",value:this.input.slice(s+2,r),start:this.sourceToOffsetPos(s),end:this.sourceToOffsetPos(r+e.length),loc:new i(t,this.state.curPosition())};return 256&this.optionFlags&&this.pushToken(a),a}skipLineComment(e){let t,s=this.state.pos;this.isLookahead||(t=this.state.curPosition());let r=this.input.charCodeAt(this.state.pos+=e);if(this.state.pos<this.length)for(;!eu(r)&&++this.state.pos<this.length;)r=this.input.charCodeAt(this.state.pos);if(this.isLookahead)return;let a=this.state.pos,n={type:"CommentLine",value:this.input.slice(s+e,a),start:this.sourceToOffsetPos(s),end:this.sourceToOffsetPos(a),loc:new i(t,this.state.curPosition())};return 256&this.optionFlags&&this.pushToken(n),n}skipSpace(){let e=this.state.pos,t=4096&this.optionFlags?[]:null;e:for(;this.state.pos<this.length;){let s=this.input.charCodeAt(this.state.pos);switch(s){case 32:case 160:case 9:++this.state.pos;break;case 13:10===this.input.charCodeAt(this.state.pos+1)&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:{let e=this.skipBlockComment("*/");void 0!==e&&(this.addComment(e),null==t||t.push(e));break}case 47:{let e=this.skipLineComment(2);void 0!==e&&(this.addComment(e),null==t||t.push(e));break}default:break e}break;default:if(function(e){switch(e){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}(s))++this.state.pos;else if(45===s&&!this.inModule&&8192&this.optionFlags){let s=this.state.pos;if(45===this.input.charCodeAt(s+1)&&62===this.input.charCodeAt(s+2)&&(0===e||this.state.lineStart>e)){let e=this.skipLineComment(3);void 0!==e&&(this.addComment(e),null==t||t.push(e))}else break e}else if(60===s&&!this.inModule&&8192&this.optionFlags){let e=this.state.pos;if(33===this.input.charCodeAt(e+1)&&45===this.input.charCodeAt(e+2)&&45===this.input.charCodeAt(e+3)){let e=this.skipLineComment(4);void 0!==e&&(this.addComment(e),null==t||t.push(e))}else break e}else break e}}if((null==t?void 0:t.length)>0){let s=this.state.pos,i={start:this.sourceToOffsetPos(e),end:this.sourceToOffsetPos(s),comments:t,leadingNode:null,trailingNode:null,containingNode:null};this.state.commentStack.push(i)}}finishToken(e,t){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();let s=this.state.type;this.state.type=e,this.state.value=t,this.isLookahead||this.updateContext(s)}replaceToken(e){this.state.type=e,this.updateContext()}readToken_numberSign(){if(0===this.state.pos&&this.readToken_interpreter())return;let e=this.state.pos+1,t=this.codePointAtPos(e);if(t>=48&&t<=57)throw this.raise(d.UnexpectedDigitAfterHash,this.state.curPosition());if(123===t||91===t&&this.hasPlugin("recordAndTuple")){if(this.expectPlugin("recordAndTuple"),"bar"===this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(123===t?d.RecordExpressionHashIncorrectStartSyntaxType:d.TupleExpressionHashIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,123===t?this.finishToken(7):this.finishToken(1)}else K(t)?(++this.state.pos,this.finishToken(139,this.readWord1(t))):92===t?(++this.state.pos,this.finishToken(139,this.readWord1())):this.finishOp(27,1)}readToken_dot(){let e=this.input.charCodeAt(this.state.pos+1);if(e>=48&&e<=57)return void this.readNumber(!0);46===e&&46===this.input.charCodeAt(this.state.pos+2)?(this.state.pos+=3,this.finishToken(21)):(++this.state.pos,this.finishToken(16))}readToken_slash(){61===this.input.charCodeAt(this.state.pos+1)?this.finishOp(31,2):this.finishOp(56,1)}readToken_interpreter(){if(0!==this.state.pos||this.length<2)return!1;let e=this.input.charCodeAt(this.state.pos+1);if(33!==e)return!1;let t=this.state.pos;for(this.state.pos+=1;!eu(e)&&++this.state.pos<this.length;)e=this.input.charCodeAt(this.state.pos);let s=this.input.slice(t+2,this.state.pos);return this.finishToken(28,s),!0}readToken_mult_modulo(e){let t=42===e?55:54,s=1,i=this.input.charCodeAt(this.state.pos+1);42===e&&42===i&&(s++,i=this.input.charCodeAt(this.state.pos+2),t=57),61!==i||this.state.inType||(s++,t=37===e?33:30),this.finishOp(t,s)}readToken_pipe_amp(e){let t=this.input.charCodeAt(this.state.pos+1);if(t===e)return void(61===this.input.charCodeAt(this.state.pos+2)?this.finishOp(30,3):this.finishOp(124===e?41:42,2));if(124===e){if(62===t)return void this.finishOp(39,2);if(this.hasPlugin("recordAndTuple")&&125===t){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(d.RecordExpressionBarIncorrectEndSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(9);return}if(this.hasPlugin("recordAndTuple")&&93===t){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(d.TupleExpressionBarIncorrectEndSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(4);return}}if(61===t)return void this.finishOp(30,2);this.finishOp(124===e?43:45,1)}readToken_caret(){let e=this.input.charCodeAt(this.state.pos+1);61!==e||this.state.inType?94===e&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"^^"}])?(this.finishOp(37,2),94===this.input.codePointAt(this.state.pos)&&this.unexpected()):this.finishOp(44,1):this.finishOp(32,2)}readToken_atSign(){64===this.input.charCodeAt(this.state.pos+1)&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"@@"}])?this.finishOp(38,2):this.finishOp(26,1)}readToken_plus_min(e){let t=this.input.charCodeAt(this.state.pos+1);if(t===e)return void this.finishOp(34,2);61===t?this.finishOp(30,2):this.finishOp(53,1)}readToken_lt(){let{pos:e}=this.state,t=this.input.charCodeAt(e+1);return 60===t?61===this.input.charCodeAt(e+2)?void this.finishOp(30,3):void this.finishOp(51,2):61===t?void this.finishOp(49,2):void this.finishOp(47,1)}readToken_gt(){let{pos:e}=this.state,t=this.input.charCodeAt(e+1);if(62===t){let t=62===this.input.charCodeAt(e+2)?3:2;return 61===this.input.charCodeAt(e+t)?void this.finishOp(30,t+1):void this.finishOp(52,t)}if(61===t)return void this.finishOp(49,2);this.finishOp(48,1)}readToken_eq_excl(e){let t=this.input.charCodeAt(this.state.pos+1);if(61===t)return void this.finishOp(46,61===this.input.charCodeAt(this.state.pos+2)?3:2);if(61===e&&62===t){this.state.pos+=2,this.finishToken(19);return}this.finishOp(61===e?29:35,1)}readToken_question(){let e=this.input.charCodeAt(this.state.pos+1),t=this.input.charCodeAt(this.state.pos+2);63===e?61===t?this.finishOp(30,3):this.finishOp(40,2):46!==e||t>=48&&t<=57?(++this.state.pos,this.finishToken(17)):(this.state.pos+=2,this.finishToken(18))}getTokenFromCode(e){switch(e){case 46:this.readToken_dot();return;case 40:++this.state.pos,this.finishToken(10);return;case 41:++this.state.pos,this.finishToken(11);return;case 59:++this.state.pos,this.finishToken(13);return;case 44:++this.state.pos,this.finishToken(12);return;case 91:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(d.TupleExpressionBarIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(2)}else++this.state.pos,this.finishToken(0);return;case 93:++this.state.pos,this.finishToken(3);return;case 123:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(d.RecordExpressionBarIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(6)}else++this.state.pos,this.finishToken(5);return;case 125:++this.state.pos,this.finishToken(8);return;case 58:this.hasPlugin("functionBind")&&58===this.input.charCodeAt(this.state.pos+1)?this.finishOp(15,2):(++this.state.pos,this.finishToken(14));return;case 63:this.readToken_question();return;case 96:this.readTemplateToken();return;case 48:{let e=this.input.charCodeAt(this.state.pos+1);if(120===e||88===e)return void this.readRadixNumber(16);if(111===e||79===e)return void this.readRadixNumber(8);if(98===e||66===e)return void this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:this.readNumber(!1);return;case 34:case 39:this.readString(e);return;case 47:this.readToken_slash();return;case 37:case 42:this.readToken_mult_modulo(e);return;case 124:case 38:this.readToken_pipe_amp(e);return;case 94:this.readToken_caret();return;case 43:case 45:this.readToken_plus_min(e);return;case 60:this.readToken_lt();return;case 62:this.readToken_gt();return;case 61:case 33:this.readToken_eq_excl(e);return;case 126:this.finishOp(36,1);return;case 64:this.readToken_atSign();return;case 35:this.readToken_numberSign();return;case 92:this.readWord();return;default:if(K(e))return void this.readWord(e)}throw this.raise(d.InvalidOrUnexpectedToken,this.state.curPosition(),{unexpected:String.fromCodePoint(e)})}finishOp(e,t){let s=this.input.slice(this.state.pos,this.state.pos+t);this.state.pos+=t,this.finishToken(e,s)}readRegexp(){let e,t,s=this.state.startLoc,i=this.state.start+1,{pos:a}=this.state;for(;;++a){if(a>=this.length)throw this.raise(d.UnterminatedRegExp,r(s,1));let i=this.input.charCodeAt(a);if(eu(i))throw this.raise(d.UnterminatedRegExp,r(s,1));if(e)e=!1;else{if(91===i)t=!0;else if(93===i&&t)t=!1;else if(47===i&&!t)break;e=92===i}}let n=this.input.slice(i,a);++a;let o="",h=()=>r(s,a+2-i);for(;a<this.length;){let e=this.codePointAtPos(a),t=String.fromCharCode(e);if(eS.has(e))118===e?o.includes("u")&&this.raise(d.IncompatibleRegExpUVFlags,h()):117===e&&o.includes("v")&&this.raise(d.IncompatibleRegExpUVFlags,h()),o.includes(t)&&this.raise(d.DuplicateRegExpFlags,h());else if(J(e)||92===e)this.raise(d.MalformedRegExpFlags,h());else break;++a,o+=t}this.state.pos=a,this.finishToken(138,{pattern:n,flags:o})}readInt(e,t,s=!1,i=!0){let{n:r,pos:a}=eb(this.input,this.state.pos,this.state.lineStart,this.state.curLine,e,t,s,i,this.errorHandlers_readInt,!1);return this.state.pos=a,r}readRadixNumber(e){let t=this.state.pos,s=this.state.curPosition(),i=!1;this.state.pos+=2;let a=this.readInt(e);null==a&&this.raise(d.InvalidDigit,r(s,2),{radix:e});let n=this.input.charCodeAt(this.state.pos);if(110===n)++this.state.pos,i=!0;else if(109===n)throw this.raise(d.InvalidDecimal,s);if(K(this.codePointAtPos(this.state.pos)))throw this.raise(d.NumberIdentifier,this.state.curPosition());if(i){let e=this.input.slice(t,this.state.pos).replace(/[_n]/g,"");this.finishToken(136,e);return}this.finishToken(135,a)}readNumber(e){let t=this.state.pos,s=this.state.curPosition(),i=!1,a=!1,n=!1,o=!1;e||null!==this.readInt(10)||this.raise(d.InvalidNumber,this.state.curPosition());let h=this.state.pos-t>=2&&48===this.input.charCodeAt(t);if(h){let e=this.input.slice(t,this.state.pos);if(this.recordStrictModeErrors(d.StrictOctalLiteral,s),!this.state.strict){let t=e.indexOf("_");t>0&&this.raise(d.ZeroDigitNumericSeparator,r(s,t))}o=h&&!/[89]/.test(e)}let l=this.input.charCodeAt(this.state.pos);if(46!==l||o||(++this.state.pos,this.readInt(10),i=!0,l=this.input.charCodeAt(this.state.pos)),69!==l&&101!==l||o||((43===(l=this.input.charCodeAt(++this.state.pos))||45===l)&&++this.state.pos,null===this.readInt(10)&&this.raise(d.InvalidOrMissingExponent,s),i=!0,n=!0,l=this.input.charCodeAt(this.state.pos)),110===l&&((i||h)&&this.raise(d.InvalidBigIntLiteral,s),++this.state.pos,a=!0),109===l){this.expectPlugin("decimal",this.state.curPosition()),(n||h)&&this.raise(d.InvalidDecimal,s),++this.state.pos;var p=!0}if(K(this.codePointAtPos(this.state.pos)))throw this.raise(d.NumberIdentifier,this.state.curPosition());let c=this.input.slice(t,this.state.pos).replace(/[_mn]/g,"");if(a)return void this.finishToken(136,c);if(p)return void this.finishToken(137,c);let u=o?parseInt(c,8):parseFloat(c);this.finishToken(135,u)}readCodePoint(e){let{code:t,pos:s}=eA(this.input,this.state.pos,this.state.lineStart,this.state.curLine,e,this.errorHandlers_readCodePoint);return this.state.pos=s,t}readString(e){let{str:t,pos:s,curLine:i,lineStart:r}=eg(34===e?"double":"single",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_string);this.state.pos=s+1,this.state.lineStart=r,this.state.curLine=i,this.finishToken(134,t)}readTemplateContinuation(){this.match(8)||this.unexpected(null,8),this.state.pos--,this.readTemplateToken()}readTemplateToken(){let e=this.input[this.state.pos],{str:t,firstInvalidLoc:i,pos:r,curLine:a,lineStart:n}=eg("template",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_template);this.state.pos=r+1,this.state.lineStart=n,this.state.curLine=a,i&&(this.state.firstInvalidTemplateEscapePos=new s(i.curLine,i.pos-i.lineStart,this.sourceToOffsetPos(i.pos))),96===this.input.codePointAt(r)?this.finishToken(24,i?null:e+t+"`"):(this.state.pos++,this.finishToken(25,i?null:e+t+"${"))}recordStrictModeErrors(e,t){let s=t.index;this.state.strict&&!this.state.strictErrors.has(s)?this.raise(e,t):this.state.strictErrors.set(s,[e,t])}readWord1(e){this.state.containsEsc=!1;let t="",s=this.state.pos,i=this.state.pos;for(void 0!==e&&(this.state.pos+=e<=65535?1:2);this.state.pos<this.length;){let e=this.codePointAtPos(this.state.pos);if(J(e))this.state.pos+=e<=65535?1:2;else if(92===e){this.state.containsEsc=!0,t+=this.input.slice(i,this.state.pos);let e=this.state.curPosition(),r=this.state.pos===s?K:J;if(117!==this.input.charCodeAt(++this.state.pos)){this.raise(d.MissingUnicodeEscape,this.state.curPosition()),i=this.state.pos-1;continue}++this.state.pos;let a=this.readCodePoint(!0);null!==a&&(r(a)||this.raise(d.EscapedCharNotAnIdentifier,e),t+=String.fromCodePoint(a)),i=this.state.pos}else break}return t+this.input.slice(i,this.state.pos)}readWord(e){let t=this.readWord1(e),s=T.get(t);void 0!==s?this.finishToken(s,I[s]):this.finishToken(132,t)}checkKeywordEscapes(){let{type:e}=this.state;R(e)&&this.state.containsEsc&&this.raise(d.InvalidEscapedReservedWord,this.state.startLoc,{reservedWord:I[e]})}raise(e,t,i={}){let r=e(t instanceof s?t:t.loc.start,i);if(!(2048&this.optionFlags))throw r;return this.isLookahead||this.state.errors.push(r),r}raiseOverwrite(e,t,i={}){let r=t instanceof s?t:t.loc.start,a=r.index,n=this.state.errors;for(let t=n.length-1;t>=0;t--){let s=n[t];if(s.loc.index===a)return n[t]=e(r,i);if(s.loc.index<a)break}return this.raise(e,t,i)}updateContext(e){}unexpected(e,t){throw this.raise(d.UnexpectedToken,null!=e?e:this.state.startLoc,{expected:t?I[t]:null})}expectPlugin(e,t){if(this.hasPlugin(e))return!0;throw this.raise(d.MissingPlugin,null!=t?t:this.state.startLoc,{missingPlugin:[e]})}expectOnePlugin(e){if(!e.some(e=>this.hasPlugin(e)))throw this.raise(d.MissingOneOfPlugins,this.state.startLoc,{missingPlugin:e})}errorBuilder(e){return(t,s,i)=>{this.raise(e,eE(t,s,i))}}}class ew{constructor(){this.privateNames=new Set,this.loneAccessors=new Map,this.undefinedPrivateNames=new Map}}class eN{constructor(e){this.parser=void 0,this.stack=[],this.undefinedPrivateNames=new Map,this.parser=e}current(){return this.stack[this.stack.length-1]}enter(){this.stack.push(new ew)}exit(){let e=this.stack.pop(),t=this.current();for(let[s,i]of Array.from(e.undefinedPrivateNames))t?t.undefinedPrivateNames.has(s)||t.undefinedPrivateNames.set(s,i):this.parser.raise(d.InvalidPrivateFieldResolution,i,{identifierName:s})}declarePrivateName(e,t,s){let{privateNames:i,loneAccessors:r,undefinedPrivateNames:a}=this.current(),n=i.has(e);if(3&t){let s=n&&r.get(e);if(s){let i=4&t;(n=(3&s)==(3&t)||(4&s)!==i)||r.delete(e)}else n||r.set(e,t)}n&&this.parser.raise(d.PrivateNameRedeclaration,s,{identifierName:e}),i.add(e),a.delete(e)}usePrivateName(e,t){let s;for(s of this.stack)if(s.privateNames.has(e))return;s?s.undefinedPrivateNames.set(e,t):this.parser.raise(d.InvalidPrivateFieldResolution,t,{identifierName:e})}}class ev{constructor(e=0){this.type=e}canBeArrowParameterDeclaration(){return 2===this.type||1===this.type}isCertainlyParameterDeclaration(){return 3===this.type}}class ek extends ev{constructor(e){super(e),this.declarationErrors=new Map}recordDeclarationError(e,t){let s=t.index;this.declarationErrors.set(s,[e,t])}clearDeclarationError(e){this.declarationErrors.delete(e)}iterateErrors(e){this.declarationErrors.forEach(e)}}class eL{constructor(e){this.parser=void 0,this.stack=[new ev],this.parser=e}enter(e){this.stack.push(e)}exit(){this.stack.pop()}recordParameterInitializerError(e,t){let s=t.loc.start,{stack:i}=this,r=i.length-1,a=i[r];for(;!a.isCertainlyParameterDeclaration();){if(!a.canBeArrowParameterDeclaration())return;a.recordDeclarationError(e,s),a=i[--r]}this.parser.raise(e,s)}recordArrowParameterBindingError(e,t){let{stack:s}=this,i=s[s.length-1],r=t.loc.start;if(i.isCertainlyParameterDeclaration())this.parser.raise(e,r);else{if(!i.canBeArrowParameterDeclaration())return;i.recordDeclarationError(e,r)}}recordAsyncArrowParametersError(e){let{stack:t}=this,s=t.length-1,i=t[s];for(;i.canBeArrowParameterDeclaration();)2===i.type&&i.recordDeclarationError(d.AwaitBindingIdentifier,e),i=t[--s]}validateAsPattern(){let{stack:e}=this,t=e[e.length-1];t.canBeArrowParameterDeclaration()&&t.iterateErrors(([t,s])=>{this.parser.raise(t,s);let i=e.length-2,r=e[i];for(;r.canBeArrowParameterDeclaration();)r.clearDeclarationError(s.index),r=e[--i]})}}class eM{constructor(){this.stacks=[]}enter(e){this.stacks.push(e)}exit(){this.stacks.pop()}currentFlags(){return this.stacks[this.stacks.length-1]}get hasAwait(){return(2&this.currentFlags())>0}get hasYield(){return(1&this.currentFlags())>0}get hasReturn(){return(4&this.currentFlags())>0}get hasIn(){return(8&this.currentFlags())>0}}function eD(e,t){return 2*!!e|!!t}class eO extends eC{addExtra(e,t,s,i=!0){if(!e)return;let{extra:r}=e;null==r&&(e.extra=r={}),i?r[t]=s:Object.defineProperty(r,t,{enumerable:i,value:s})}isContextual(e){return this.state.type===e&&!this.state.containsEsc}isUnparsedContextual(e,t){let s=e+t.length;if(this.input.slice(e,s)===t){let e=this.input.charCodeAt(s);return!(J(e)||(64512&e)==55296)}return!1}isLookaheadContextual(e){let t=this.nextTokenStart();return this.isUnparsedContextual(t,e)}eatContextual(e){return!!this.isContextual(e)&&(this.next(),!0)}expectContextual(e,t){if(!this.eatContextual(e)){if(null!=t)throw this.raise(t,this.state.startLoc);this.unexpected(null,e)}}canInsertSemicolon(){return this.match(140)||this.match(8)||this.hasPrecedingLineBreak()}hasPrecedingLineBreak(){return ed(this.input,this.offsetToSourcePos(this.state.lastTokEndLoc.index),this.state.start)}hasFollowingLineBreak(){return ed(this.input,this.state.end,this.nextTokenStart())}isLineTerminator(){return this.eat(13)||this.canInsertSemicolon()}semicolon(e=!0){(e?this.isLineTerminator():this.eat(13))||this.raise(d.MissingSemicolon,this.state.lastTokEndLoc)}expect(e,t){this.eat(e)||this.unexpected(t,e)}tryParse(e,t=this.state.clone()){let s={node:null};try{let i=e((e=null)=>{throw s.node=e,s});if(this.state.errors.length>t.errors.length){let e=this.state;return this.state=t,this.state.tokensLength=e.tokensLength,{node:i,error:e.errors[t.errors.length],thrown:!1,aborted:!1,failState:e}}return{node:i,error:null,thrown:!1,aborted:!1,failState:null}}catch(i){let e=this.state;if(this.state=t,i instanceof SyntaxError)return{node:null,error:i,thrown:!0,aborted:!1,failState:e};if(i===s)return{node:s.node,error:null,thrown:!1,aborted:!0,failState:e};throw i}}checkExpressionErrors(e,t){if(!e)return!1;let{shorthandAssignLoc:s,doubleProtoLoc:i,privateKeyLoc:r,optionalParametersLoc:a}=e,n=!!s||!!i||!!a||!!r;if(!t)return n;null!=s&&this.raise(d.InvalidCoverInitializedName,s),null!=i&&this.raise(d.DuplicateProto,i),null!=r&&this.raise(d.UnexpectedPrivateField,r),null!=a&&this.unexpected(a)}isLiteralPropertyName(){return F(this.state.type)}isPrivateName(e){return"PrivateName"===e.type}getPrivateNameSV(e){return e.id.name}hasPropertyAsPrivateName(e){return("MemberExpression"===e.type||"OptionalMemberExpression"===e.type)&&this.isPrivateName(e.property)}isObjectProperty(e){return"ObjectProperty"===e.type}isObjectMethod(e){return"ObjectMethod"===e.type}initializeScopes(e="module"===this.options.sourceType){let t=this.state.labels;this.state.labels=[];let s=this.exportedIdentifiers;this.exportedIdentifiers=new Set;let i=this.inModule;this.inModule=e;let r=this.scope,a=this.getScopeHandler();this.scope=new a(this,e);let n=this.prodParam;this.prodParam=new eM;let o=this.classScope;this.classScope=new eN(this);let h=this.expressionScope;return this.expressionScope=new eL(this),()=>{this.state.labels=t,this.exportedIdentifiers=s,this.inModule=i,this.scope=r,this.prodParam=n,this.classScope=o,this.expressionScope=h}}enterInitialScopes(){let e=0;this.inModule&&(e|=2),32&this.optionFlags&&(e|=1),this.scope.enter(1),this.prodParam.enter(e)}checkDestructuringPrivate(e){let{privateKeyLoc:t}=e;null!==t&&this.expectPlugin("destructuringPrivate",t)}}class eF{constructor(){this.shorthandAssignLoc=null,this.doubleProtoLoc=null,this.privateKeyLoc=null,this.optionalParametersLoc=null}}class eB{constructor(e,t,s){this.type="",this.start=t,this.end=0,this.loc=new i(s),(null==e?void 0:e.optionFlags)&128&&(this.range=[t,0]),null!=e&&e.filename&&(this.loc.filename=e.filename)}}let eR=eB.prototype;function eU(e){let{type:t,start:s,end:i,loc:r,range:a,extra:n,name:o}=e,h=Object.create(eR);return h.type=t,h.start=s,h.end=i,h.loc=r,h.range=a,h.extra=n,h.name=o,"Placeholder"===t&&(h.expectedNode=e.expectedNode),h}eR.__clone=function(){let e=new eB(void 0,this.start,this.loc.start),t=Object.keys(this);for(let s=0,i=t.length;s<i;s++){let i=t[s];"leadingComments"!==i&&"trailingComments"!==i&&"innerComments"!==i&&(e[i]=this[i])}return e};class ej extends eO{startNode(){let e=this.state.startLoc;return new eB(this,e.index,e)}startNodeAt(e){return new eB(this,e.index,e)}startNodeAtNode(e){return this.startNodeAt(e.loc.start)}finishNode(e,t){return this.finishNodeAt(e,t,this.state.lastTokEndLoc)}finishNodeAt(e,t,s){return e.type=t,e.end=s.index,e.loc.end=s,128&this.optionFlags&&(e.range[1]=s.index),4096&this.optionFlags&&this.processComment(e),e}resetStartLocation(e,t){e.start=t.index,e.loc.start=t,128&this.optionFlags&&(e.range[0]=t.index)}resetEndLocation(e,t=this.state.lastTokEndLoc){e.end=t.index,e.loc.end=t,128&this.optionFlags&&(e.range[1]=t.index)}resetStartLocationFromNode(e,t){this.resetStartLocation(e,t.loc.start)}}let e_=new Set(["_","any","bool","boolean","empty","extends","false","interface","mixed","null","number","static","string","true","typeof","void"]),eH=u`flow`({AmbiguousConditionalArrow:"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.",AmbiguousDeclareModuleKind:"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.",AssignReservedType:({reservedType:e})=>`Cannot overwrite reserved type ${e}.`,DeclareClassElement:"The `declare` modifier can only appear on class fields.",DeclareClassFieldInitializer:"Initializers are not allowed in fields with the `declare` modifier.",DuplicateDeclareModuleExports:"Duplicate `declare module.exports` statement.",EnumBooleanMemberNotInitialized:({memberName:e,enumName:t})=>`Boolean enum members need to be initialized. Use either \`${e} = true,\` or \`${e} = false,\` in enum \`${t}\`.`,EnumDuplicateMemberName:({memberName:e,enumName:t})=>`Enum member names need to be unique, but the name \`${e}\` has already been used before in enum \`${t}\`.`,EnumInconsistentMemberValues:({enumName:e})=>`Enum \`${e}\` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.`,EnumInvalidExplicitType:({invalidEnumType:e,enumName:t})=>`Enum type \`${e}\` is not valid. Use one of \`boolean\`, \`number\`, \`string\`, or \`symbol\` in enum \`${t}\`.`,EnumInvalidExplicitTypeUnknownSupplied:({enumName:e})=>`Supplied enum type is not valid. Use one of \`boolean\`, \`number\`, \`string\`, or \`symbol\` in enum \`${e}\`.`,EnumInvalidMemberInitializerPrimaryType:({enumName:e,memberName:t,explicitType:s})=>`Enum \`${e}\` has type \`${s}\`, so the initializer of \`${t}\` needs to be a ${s} literal.`,EnumInvalidMemberInitializerSymbolType:({enumName:e,memberName:t})=>`Symbol enum members cannot be initialized. Use \`${t},\` in enum \`${e}\`.`,EnumInvalidMemberInitializerUnknownType:({enumName:e,memberName:t})=>`The enum member initializer for \`${t}\` needs to be a literal (either a boolean, number, or string) in enum \`${e}\`.`,EnumInvalidMemberName:({enumName:e,memberName:t,suggestion:s})=>`Enum member names cannot start with lowercase 'a' through 'z'. Instead of using \`${t}\`, consider using \`${s}\`, in enum \`${e}\`.`,EnumNumberMemberNotInitialized:({enumName:e,memberName:t})=>`Number enum members need to be initialized, e.g. \`${t} = 1\` in enum \`${e}\`.`,EnumStringMemberInconsistentlyInitialized:({enumName:e})=>`String enum members need to consistently either all use initializers, or use no initializers, in enum \`${e}\`.`,GetterMayNotHaveThisParam:"A getter cannot have a `this` parameter.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` or `typeof` keyword.",ImportTypeShorthandOnlyInPureImport:"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.",InexactInsideExact:"Explicit inexact syntax cannot appear inside an explicit exact object type.",InexactInsideNonObject:"Explicit inexact syntax cannot appear in class or interface definitions.",InexactVariance:"Explicit inexact syntax cannot have variance.",InvalidNonTypeImportInDeclareModule:"Imports within a `declare module` body must always be `import type` or `import typeof`.",MissingTypeParamDefault:"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",NestedDeclareModule:"`declare module` cannot be used inside another `declare module`.",NestedFlowComment:"Cannot have a flow comment inside another flow comment.",PatternIsOptional:Object.assign({message:"A binding pattern parameter cannot be optional in an implementation signature."},{reasonCode:"OptionalBindingPattern"}),SetterMayNotHaveThisParam:"A setter cannot have a `this` parameter.",SpreadVariance:"Spread properties cannot have variance.",ThisParamAnnotationRequired:"A type annotation is required for the `this` parameter.",ThisParamBannedInConstructor:"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.",ThisParamMayNotBeOptional:"The `this` parameter cannot be optional.",ThisParamMustBeFirst:"The `this` parameter must be the first function parameter.",ThisParamNoDefault:"The `this` parameter may not have a default value.",TypeBeforeInitializer:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeCastInPattern:"The type cast expression is expected to be wrapped with parenthesis.",UnexpectedExplicitInexactInObject:"Explicit inexact syntax must appear at the end of an inexact object.",UnexpectedReservedType:({reservedType:e})=>`Unexpected reserved type ${e}.`,UnexpectedReservedUnderscore:"`_` is only allowed as a type argument to call or new.",UnexpectedSpaceBetweenModuloChecks:"Spaces between `%` and `checks` are not allowed here.",UnexpectedSpreadType:"Spread operator cannot appear in class or interface definitions.",UnexpectedSubtractionOperand:'Unexpected token, expected "number" or "bigint".',UnexpectedTokenAfterTypeParameter:"Expected an arrow function after this type parameter declaration.",UnexpectedTypeParameterBeforeAsyncArrowFunction:"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.",UnsupportedDeclareExportKind:({unsupportedExportKind:e,suggestion:t})=>`\`declare export ${e}\` is not supported. Use \`${t}\` instead.`,UnsupportedStatementInDeclareModule:"Only declares and type imports are allowed inside declare module.",UnterminatedFlowComment:"Unterminated flow-comment."});function ez(e){return"type"===e.importKind||"typeof"===e.importKind}let eV={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"},eq=/\*?\s*@((?:no)?flow)\b/,e$={__proto__:null,quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"},eK=u`jsx`({AttributeIsEmpty:"JSX attributes must only be assigned a non-empty expression.",MissingClosingTagElement:({openingTagName:e})=>`Expected corresponding JSX closing tag for <${e}>.`,MissingClosingTagFragment:"Expected corresponding JSX closing tag for <>.",UnexpectedSequenceExpression:"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?",UnexpectedToken:({unexpected:e,HTMLEntity:t})=>`Unexpected token \`${e}\`. Did you mean \`${t}\` or \`{'${e}'}\`?`,UnsupportedJsxValue:"JSX value should be either an expression or a quoted JSX text.",UnterminatedJsxContent:"Unterminated JSX contents.",UnwrappedAdjacentJSXElements:"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?"});function eJ(e){return!!e&&("JSXOpeningFragment"===e.type||"JSXClosingFragment"===e.type)}function eW(e){if("JSXIdentifier"===e.type)return e.name;if("JSXNamespacedName"===e.type)return e.namespace.name+":"+e.name.name;if("JSXMemberExpression"===e.type)return eW(e.object)+"."+eW(e.property);throw Error("Node had unexpected type: "+e.type)}class eX extends es{constructor(...e){super(...e),this.tsNames=new Map}}class eG extends ei{constructor(...e){super(...e),this.importsStack=[]}createScope(e){return this.importsStack.push(new Set),new eX(e)}enter(e){256===e&&this.importsStack.push(new Set),super.enter(e)}exit(){let e=super.exit();return 256===e&&this.importsStack.pop(),e}hasImport(e,t){let s=this.importsStack.length;if(this.importsStack[s-1].has(e))return!0;if(!t&&s>1){for(let t=0;t<s-1;t++)if(this.importsStack[t].has(e))return!0}return!1}declareName(e,t,s){if(4096&t){this.hasImport(e,!0)&&this.parser.raise(d.VarRedeclaration,s,{identifierName:e}),this.importsStack[this.importsStack.length-1].add(e);return}let i=this.currentScope(),r=i.tsNames.get(e)||0;if(1024&t){this.maybeExportDefined(i,e),i.tsNames.set(e,16|r);return}super.declareName(e,t,s),2&t&&(1&t||(this.checkRedeclarationInScope(i,e,t,s),this.maybeExportDefined(i,e)),r|=1),256&t&&(r|=2),512&t&&(r|=4),128&t&&(r|=8),r&&i.tsNames.set(e,r)}isRedeclaredInScope(e,t,s){let i=e.tsNames.get(t);if((2&i)>0)return!(256&s)||!!(512&s)!=(4&i)>0;if(128&s&&(8&i)>0)if(2&e.names.get(t))return!!(1&s);else return!1;return!!(2&s)&&(1&i)>0||super.isRedeclaredInScope(e,t,s)}checkLocalExport(e){let{name:t}=e;if(this.hasImport(t))return;let s=this.scopeStack.length;for(let e=s-1;e>=0;e--){let s=this.scopeStack[e].tsNames.get(t);if((1&s)>0||(16&s)>0)return}super.checkLocalExport(e)}}let eY=e=>"ParenthesizedExpression"===e.type?eY(e.expression):e;class eQ extends ej{toAssignable(e,t=!1){var s,i,r;let a;switch(("ParenthesizedExpression"===e.type||null!=(s=e.extra)&&s.parenthesized)&&(a=eY(e),t?"Identifier"===a.type?this.expressionScope.recordArrowParameterBindingError(d.InvalidParenthesizedAssignment,e):"MemberExpression"!==a.type&&!this.isOptionalMemberExpression(a)&&this.raise(d.InvalidParenthesizedAssignment,e):this.raise(d.InvalidParenthesizedAssignment,e)),e.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern";for(let s=0,i=e.properties.length,a=i-1;s<i;s++){let i=e.properties[s],n=s===a;this.toAssignableObjectExpressionProp(i,n,t),n&&"RestElement"===i.type&&null!=(r=e.extra)&&r.trailingCommaLoc&&this.raise(d.RestTrailingComma,e.extra.trailingCommaLoc)}break;case"ObjectProperty":{let{key:s,value:i}=e;this.isPrivateName(s)&&this.classScope.usePrivateName(this.getPrivateNameSV(s),s.loc.start),this.toAssignable(i,t);break}case"SpreadElement":throw Error("Internal @babel/parser error (this is a bug, please report it). SpreadElement should be converted by .toAssignable's caller.");case"ArrayExpression":e.type="ArrayPattern",this.toAssignableList(e.elements,null==(i=e.extra)?void 0:i.trailingCommaLoc,t);break;case"AssignmentExpression":"="!==e.operator&&this.raise(d.MissingEqInAssignment,e.left.loc.end),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(a,t)}}toAssignableObjectExpressionProp(e,t,s){if("ObjectMethod"===e.type)this.raise("get"===e.kind||"set"===e.kind?d.PatternHasAccessor:d.PatternHasMethod,e.key);else if("SpreadElement"===e.type){e.type="RestElement";let i=e.argument;this.checkToRestConversion(i,!1),this.toAssignable(i,s),t||this.raise(d.RestTrailingComma,e)}else this.toAssignable(e,s)}toAssignableList(e,t,s){let i=e.length-1;for(let r=0;r<=i;r++){let a=e[r];a&&(this.toAssignableListItem(e,r,s),"RestElement"===a.type&&(r<i?this.raise(d.RestTrailingComma,a):t&&this.raise(d.RestTrailingComma,t)))}}toAssignableListItem(e,t,s){let i=e[t];if("SpreadElement"===i.type){i.type="RestElement";let e=i.argument;this.checkToRestConversion(e,!0),this.toAssignable(e,s)}else this.toAssignable(i,s)}isAssignable(e,t){switch(e.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":return!0;case"ObjectExpression":{let t=e.properties.length-1;return e.properties.every((e,s)=>"ObjectMethod"!==e.type&&(s===t||"SpreadElement"!==e.type)&&this.isAssignable(e))}case"ObjectProperty":return this.isAssignable(e.value);case"SpreadElement":return this.isAssignable(e.argument);case"ArrayExpression":return e.elements.every(e=>null===e||this.isAssignable(e));case"AssignmentExpression":return"="===e.operator;case"ParenthesizedExpression":return this.isAssignable(e.expression);case"MemberExpression":case"OptionalMemberExpression":return!t;default:return!1}}toReferencedList(e,t){return e}toReferencedListDeep(e,t){for(let s of(this.toReferencedList(e,t),e))(null==s?void 0:s.type)==="ArrayExpression"&&this.toReferencedListDeep(s.elements)}parseSpread(e){let t=this.startNode();return this.next(),t.argument=this.parseMaybeAssignAllowIn(e,void 0),this.finishNode(t,"SpreadElement")}parseRestBinding(){let e=this.startNode();return this.next(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")}parseBindingAtom(){switch(this.state.type){case 0:{let e=this.startNode();return this.next(),e.elements=this.parseBindingList(3,93,1),this.finishNode(e,"ArrayPattern")}case 5:return this.parseObjectLike(8,!0)}return this.parseIdentifier()}parseBindingList(e,t,s){let i=1&s,r=[],a=!0;for(;!this.eat(e);)if(a?a=!1:this.expect(12),i&&this.match(12))r.push(null);else if(this.eat(e))break;else if(this.match(21)){let i=this.parseRestBinding();if((this.hasPlugin("flow")||2&s)&&(i=this.parseFunctionParamType(i)),r.push(i),!this.checkCommaAfterRest(t)){this.expect(e);break}}else{let e=[];if(2&s)for(this.match(26)&&this.hasPlugin("decorators")&&this.raise(d.UnsupportedParameterDecorator,this.state.startLoc);this.match(26);)e.push(this.parseDecorator());r.push(this.parseBindingElement(s,e))}return r}parseBindingRestProperty(e){return this.next(),e.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(e,"RestElement")}parseBindingProperty(){let{type:e,startLoc:t}=this.state;if(21===e)return this.parseBindingRestProperty(this.startNode());let s=this.startNode();return 139===e?(this.expectPlugin("destructuringPrivate",t),this.classScope.usePrivateName(this.state.value,t),s.key=this.parsePrivateName()):this.parsePropertyName(s),s.method=!1,this.parseObjPropValue(s,t,!1,!1,!0,!1)}parseBindingElement(e,t){let s=this.parseMaybeDefault();(this.hasPlugin("flow")||2&e)&&this.parseFunctionParamType(s);let i=this.parseMaybeDefault(s.loc.start,s);return t.length&&(s.decorators=t),i}parseFunctionParamType(e){return e}parseMaybeDefault(e,t){if(null!=e||(e=this.state.startLoc),t=null!=t?t:this.parseBindingAtom(),!this.eat(29))return t;let s=this.startNodeAt(e);return s.left=t,s.right=this.parseMaybeAssignAllowIn(),this.finishNode(s,"AssignmentPattern")}isValidLVal(e,t,s){switch(e){case"AssignmentPattern":return"left";case"RestElement":return"argument";case"ObjectProperty":return"value";case"ParenthesizedExpression":return"expression";case"ArrayPattern":return"elements";case"ObjectPattern":return"properties"}return!1}isOptionalMemberExpression(e){return"OptionalMemberExpression"===e.type}checkLVal(e,t,s=64,i=!1,r=!1,a=!1){var n;let o,h,l=e.type;if(this.isObjectMethod(e))return;let p=this.isOptionalMemberExpression(e);if(p||"MemberExpression"===l){p&&(this.expectPlugin("optionalChainingAssign",e.loc.start),"AssignmentExpression"!==t.type&&this.raise(d.InvalidLhsOptionalChaining,e,{ancestor:t})),64!==s&&this.raise(d.InvalidPropertyBindingPattern,e);return}if("Identifier"===l){this.checkIdentifier(e,s,r);let{name:t}=e;i&&(i.has(t)?this.raise(d.ParamDupe,e):i.add(t));return}let c=this.isValidLVal(l,!(a||null!=(n=e.extra)&&n.parenthesized)&&"AssignmentExpression"===t.type,s);if(!0===c)return;if(!1===c){let i=64===s?d.InvalidLhs:d.InvalidLhsBinding;this.raise(i,e,{ancestor:t});return}"string"==typeof c?(o=c,h="ParenthesizedExpression"===l):[o,h]=c;let u="ArrayPattern"===l||"ObjectPattern"===l?{type:l}:t,m=e[o];if(Array.isArray(m))for(let e of m)e&&this.checkLVal(e,u,s,i,r,h);else m&&this.checkLVal(m,u,s,i,r,h)}checkIdentifier(e,t,s=!1){var i;this.state.strict&&(s?ee(e.name,this.inModule):(i=e.name,Y.has(i)))&&(64===t?this.raise(d.StrictEvalArguments,e,{referenceName:e.name}):this.raise(d.StrictEvalArgumentsBinding,e,{bindingName:e.name})),8192&t&&"let"===e.name&&this.raise(d.LetInLexicalBinding,e),64&t||this.declareNameFromIdentifier(e,t)}declareNameFromIdentifier(e,t){this.scope.declareName(e.name,t,e.loc.start)}checkToRestConversion(e,t){switch(e.type){case"ParenthesizedExpression":this.checkToRestConversion(e.expression,t);break;case"Identifier":case"MemberExpression":break;case"ArrayExpression":case"ObjectExpression":if(t)break;default:this.raise(d.InvalidRestAssignmentPattern,e)}}checkCommaAfterRest(e){return!!this.match(12)&&(this.raise(this.lookaheadCharCode()===e?d.RestTrailingComma:d.ElementAfterRest,this.state.startLoc),!0)}}function eZ(e){if(!e)throw Error("Assert fail")}let e1=u`typescript`({AbstractMethodHasImplementation:({methodName:e})=>`Method '${e}' cannot have an implementation because it is marked abstract.`,AbstractPropertyHasInitializer:({propertyName:e})=>`Property '${e}' cannot have an initializer because it is marked abstract.`,AccessorCannotBeOptional:"An 'accessor' property cannot be declared optional.",AccessorCannotDeclareThisParameter:"'get' and 'set' accessors cannot declare 'this' parameters.",AccessorCannotHaveTypeParameters:"An accessor cannot have type parameters.",ClassMethodHasDeclare:"Class methods cannot have the 'declare' modifier.",ClassMethodHasReadonly:"Class methods cannot have the 'readonly' modifier.",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:"A 'const' initializer in an ambient context must be a string or numeric literal or literal enum reference.",ConstructorHasTypeParameters:"Type parameters cannot appear on a constructor declaration.",DeclareAccessor:({kind:e})=>`'declare' is not allowed in ${e}ters.`,DeclareClassFieldHasInitializer:"Initializers are not allowed in ambient contexts.",DeclareFunctionHasImplementation:"An implementation cannot be declared in ambient contexts.",DuplicateAccessibilityModifier:({modifier:e})=>"Accessibility modifier already seen.",DuplicateModifier:({modifier:e})=>`Duplicate modifier: '${e}'.`,EmptyHeritageClauseType:({token:e})=>`'${e}' list cannot be empty.`,EmptyTypeArguments:"Type argument list cannot be empty.",EmptyTypeParameters:"Type parameter list cannot be empty.",ExpectedAmbientAfterExportDeclare:"'export declare' must be followed by an ambient declaration.",ImportAliasHasImportType:"An import alias can not use 'import type'.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` modifier",IncompatibleModifiers:({modifiers:e})=>`'${e[0]}' modifier cannot be used with '${e[1]}' modifier.`,IndexSignatureHasAbstract:"Index signatures cannot have the 'abstract' modifier.",IndexSignatureHasAccessibility:({modifier:e})=>`Index signatures cannot have an accessibility modifier ('${e}').`,IndexSignatureHasDeclare:"Index signatures cannot have the 'declare' modifier.",IndexSignatureHasOverride:"'override' modifier cannot appear on an index signature.",IndexSignatureHasStatic:"Index signatures cannot have the 'static' modifier.",InitializerNotAllowedInAmbientContext:"Initializers are not allowed in ambient contexts.",InvalidHeritageClauseType:({token:e})=>`'${e}' list can only include identifiers or qualified-names with optional type arguments.`,InvalidModifierOnTypeMember:({modifier:e})=>`'${e}' modifier cannot appear on a type member.`,InvalidModifierOnTypeParameter:({modifier:e})=>`'${e}' modifier cannot appear on a type parameter.`,InvalidModifierOnTypeParameterPositions:({modifier:e})=>`'${e}' modifier can only appear on a type parameter of a class, interface or type alias.`,InvalidModifiersOrder:({orderedModifiers:e})=>`'${e[0]}' modifier must precede '${e[1]}' modifier.`,InvalidPropertyAccessAfterInstantiationExpression:"Invalid property access after an instantiation expression. You can either wrap the instantiation expression in parentheses, or delete the type arguments.",InvalidTupleMemberLabel:"Tuple members must be labeled with a simple identifier.",MissingInterfaceName:"'interface' declarations must be followed by an identifier.",NonAbstractClassHasAbstractMethod:"Abstract methods can only appear within an abstract class.",NonClassMethodPropertyHasAbstractModifer:"'abstract' modifier can only appear on a class, method, or property declaration.",OptionalTypeBeforeRequired:"A required element cannot follow an optional element.",OverrideNotInSubClass:"This member cannot have an 'override' modifier because its containing class does not extend another class.",PatternIsOptional:"A binding pattern parameter cannot be optional in an implementation signature.",PrivateElementHasAbstract:"Private elements cannot have the 'abstract' modifier.",PrivateElementHasAccessibility:({modifier:e})=>`Private elements cannot have an accessibility modifier ('${e}').`,ReadonlyForMethodSignature:"'readonly' modifier can only appear on a property declaration or index signature.",ReservedArrowTypeParam:"This syntax is reserved in files with the .mts or .cts extension. Add a trailing comma, as in `<T,>() => ...`.",ReservedTypeAssertion:"This syntax is reserved in files with the .mts or .cts extension. Use an `as` expression instead.",SetAccessorCannotHaveOptionalParameter:"A 'set' accessor cannot have an optional parameter.",SetAccessorCannotHaveRestParameter:"A 'set' accessor cannot have rest parameter.",SetAccessorCannotHaveReturnType:"A 'set' accessor cannot have a return type annotation.",SingleTypeParameterWithoutTrailingComma:({typeParameterName:e})=>`Single type parameter ${e} should have a trailing comma. Example usage: <${e},>.`,StaticBlockCannotHaveModifier:"Static class blocks cannot have any modifier.",TupleOptionalAfterType:"A labeled tuple optional element must be declared using a question mark after the name and before the colon (`name?: type`), rather than after the type (`name: type?`).",TypeAnnotationAfterAssign:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeImportCannotSpecifyDefaultAndNamed:"A type-only import can specify a default import or named bindings, but not both.",TypeModifierIsUsedInTypeExports:"The 'type' modifier cannot be used on a named export when 'export type' is used on its export statement.",TypeModifierIsUsedInTypeImports:"The 'type' modifier cannot be used on a named import when 'import type' is used on its import statement.",UnexpectedParameterModifier:"A parameter property is only allowed in a constructor implementation.",UnexpectedReadonly:"'readonly' type modifier is only permitted on array and tuple literal types.",UnexpectedTypeAnnotation:"Did not expect a type annotation here.",UnexpectedTypeCastInParameter:"Unexpected type cast in parameter position.",UnsupportedImportTypeArgument:"Argument in a type import must be a string literal.",UnsupportedParameterPropertyKind:"A parameter property may not be declared using a binding pattern.",UnsupportedSignatureParameterKind:({type:e})=>`Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got ${e}.`});function e0(e){return"private"===e||"public"===e||"protected"===e}function e2(e,t){return t?"Literal"===e.type&&("number"==typeof e.value||"bigint"in e):"NumericLiteral"===e.type||"BigIntLiteral"===e.type}let e4=u`placeholders`({ClassNameIsRequired:"A class name is required.",UnexpectedSpace:"Unexpected space in placeholder."}),e3=["minimal","fsharp","hack","smart"],e5=["^^","@@","^","%","#"],e8={estree:e=>class extends e{parse(){let e=y(super.parse());return 256&this.optionFlags&&(e.tokens=e.tokens.map(y)),e}parseRegExpLiteral({pattern:e,flags:t}){let s=null;try{s=new RegExp(e,t)}catch(e){}let i=this.estreeParseLiteral(s);return i.regex={pattern:e,flags:t},i}parseBigIntLiteral(e){let t;try{t=BigInt(e)}catch(e){t=null}let s=this.estreeParseLiteral(t);return s.bigint=String(s.value||e),s}parseDecimalLiteral(e){let t=this.estreeParseLiteral(null);return t.decimal=String(t.value||e),t}estreeParseLiteral(e){return this.parseLiteral(e,"Literal")}parseStringLiteral(e){return this.estreeParseLiteral(e)}parseNumericLiteral(e){return this.estreeParseLiteral(e)}parseNullLiteral(){return this.estreeParseLiteral(null)}parseBooleanLiteral(e){return this.estreeParseLiteral(e)}directiveToStmt(e){let t=e.value;return delete e.value,t.type="Literal",t.raw=t.extra.raw,t.value=t.extra.expressionValue,e.type="ExpressionStatement",e.expression=t,e.directive=t.extra.rawValue,delete t.extra,e}initFunction(e,t){super.initFunction(e,t),e.expression=!1}checkDeclaration(e){null!=e&&this.isObjectProperty(e)?this.checkDeclaration(e.value):super.checkDeclaration(e)}getObjectOrClassMethodParams(e){return e.value.params}isValidDirective(e){var t;return"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&!(null!=(t=e.expression.extra)&&t.parenthesized)}parseBlockBody(e,t,s,i,r){super.parseBlockBody(e,t,s,i,r);let a=e.directives.map(e=>this.directiveToStmt(e));e.body=a.concat(e.body),delete e.directives}parsePrivateName(){let e=super.parsePrivateName();return this.getPluginOption("estree","classFeatures")?this.convertPrivateNameToPrivateIdentifier(e):e}convertPrivateNameToPrivateIdentifier(e){let t=super.getPrivateNameSV(e);return delete e.id,e.name=t,e.type="PrivateIdentifier",e}isPrivateName(e){return this.getPluginOption("estree","classFeatures")?"PrivateIdentifier"===e.type:super.isPrivateName(e)}getPrivateNameSV(e){return this.getPluginOption("estree","classFeatures")?e.name:super.getPrivateNameSV(e)}parseLiteral(e,t){let s=super.parseLiteral(e,t);return s.raw=s.extra.raw,delete s.extra,s}parseFunctionBody(e,t,s=!1){super.parseFunctionBody(e,t,s),e.expression="BlockStatement"!==e.body.type}parseMethod(e,t,s,i,r,a,n=!1){let o=this.startNode();o.kind=e.kind,(o=super.parseMethod(o,t,s,i,r,a,n)).type="FunctionExpression",delete o.kind,e.value=o;let{typeParameters:h}=e;return h&&(delete e.typeParameters,o.typeParameters=h,this.resetStartLocationFromNode(o,h)),"ClassPrivateMethod"===a&&(e.computed=!1),this.finishNode(e,"MethodDefinition")}nameIsConstructor(e){return"Literal"===e.type?"constructor"===e.value:super.nameIsConstructor(e)}parseClassProperty(...e){let t=super.parseClassProperty(...e);return this.getPluginOption("estree","classFeatures")&&(t.type="PropertyDefinition"),t}parseClassPrivateProperty(...e){let t=super.parseClassPrivateProperty(...e);return this.getPluginOption("estree","classFeatures")&&(t.type="PropertyDefinition",t.computed=!1),t}parseClassAccessorProperty(e){let t=super.parseClassAccessorProperty(e);return this.getPluginOption("estree","classFeatures")&&(t.type="AccessorProperty"),t}parseObjectMethod(e,t,s,i,r){let a=super.parseObjectMethod(e,t,s,i,r);return a&&(a.type="Property","method"===a.kind&&(a.kind="init"),a.shorthand=!1),a}parseObjectProperty(e,t,s,i){let r=super.parseObjectProperty(e,t,s,i);return r&&(r.kind="init",r.type="Property"),r}isValidLVal(e,t,s){return"Property"===e?"value":super.isValidLVal(e,t,s)}isAssignable(e,t){return null!=e&&this.isObjectProperty(e)?this.isAssignable(e.value,t):super.isAssignable(e,t)}toAssignable(e,t=!1){if(null!=e&&this.isObjectProperty(e)){let{key:s,value:i}=e;this.isPrivateName(s)&&this.classScope.usePrivateName(this.getPrivateNameSV(s),s.loc.start),this.toAssignable(i,t)}else super.toAssignable(e,t)}toAssignableObjectExpressionProp(e,t,s){"Property"===e.type&&("get"===e.kind||"set"===e.kind)?this.raise(d.PatternHasAccessor,e.key):"Property"===e.type&&e.method?this.raise(d.PatternHasMethod,e.key):super.toAssignableObjectExpressionProp(e,t,s)}finishCallExpression(e,t){let s=super.finishCallExpression(e,t);if("Import"===s.callee.type){var i,r;s.type="ImportExpression",s.source=s.arguments[0],s.options=null!=(i=s.arguments[1])?i:null,s.attributes=null!=(r=s.arguments[1])?r:null,delete s.arguments,delete s.callee}return s}toReferencedArguments(e){"ImportExpression"!==e.type&&super.toReferencedArguments(e)}parseExport(e,t){let s=this.state.lastTokStartLoc,i=super.parseExport(e,t);switch(i.type){case"ExportAllDeclaration":i.exported=null;break;case"ExportNamedDeclaration":1===i.specifiers.length&&"ExportNamespaceSpecifier"===i.specifiers[0].type&&(i.type="ExportAllDeclaration",i.exported=i.specifiers[0].exported,delete i.specifiers);case"ExportDefaultDeclaration":{var r;let{declaration:e}=i;(null==e?void 0:e.type)==="ClassDeclaration"&&(null==(r=e.decorators)?void 0:r.length)>0&&e.start===i.start&&this.resetStartLocation(i,s)}}return i}parseSubscript(e,t,s,i){let r=super.parseSubscript(e,t,s,i);if(i.optionalChainMember){if(("OptionalMemberExpression"===r.type||"OptionalCallExpression"===r.type)&&(r.type=r.type.substring(8)),i.stop){let e=this.startNodeAtNode(r);return e.expression=r,this.finishNode(e,"ChainExpression")}}else("MemberExpression"===r.type||"CallExpression"===r.type)&&(r.optional=!1);return r}isOptionalMemberExpression(e){return"ChainExpression"===e.type?"MemberExpression"===e.expression.type:super.isOptionalMemberExpression(e)}hasPropertyAsPrivateName(e){return"ChainExpression"===e.type&&(e=e.expression),super.hasPropertyAsPrivateName(e)}isObjectProperty(e){return"Property"===e.type&&"init"===e.kind&&!e.method}isObjectMethod(e){return"Property"===e.type&&(e.method||"get"===e.kind||"set"===e.kind)}finishNodeAt(e,t,s){return y(super.finishNodeAt(e,t,s))}resetStartLocation(e,t){super.resetStartLocation(e,t),y(e)}resetEndLocation(e,t=this.state.lastTokEndLoc){super.resetEndLocation(e,t),y(e)}},jsx:e=>class extends e{jsxReadToken(){let e="",t=this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(eK.UnterminatedJsxContent,this.state.startLoc);let s=this.input.charCodeAt(this.state.pos);switch(s){case 60:case 123:if(this.state.pos===this.state.start)return void(60===s&&this.state.canStartJSXElement?(++this.state.pos,this.finishToken(143)):super.getTokenFromCode(s));e+=this.input.slice(t,this.state.pos),this.finishToken(142,e);return;case 38:e+=this.input.slice(t,this.state.pos),e+=this.jsxReadEntity(),t=this.state.pos;break;default:eu(s)?(e+=this.input.slice(t,this.state.pos),e+=this.jsxReadNewLine(!0),t=this.state.pos):++this.state.pos}}}jsxReadNewLine(e){let t,s=this.input.charCodeAt(this.state.pos);return++this.state.pos,13===s&&10===this.input.charCodeAt(this.state.pos)?(++this.state.pos,t=e?"\n":"\r\n"):t=String.fromCharCode(s),++this.state.curLine,this.state.lineStart=this.state.pos,t}jsxReadString(e){let t="",s=++this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(d.UnterminatedString,this.state.startLoc);let i=this.input.charCodeAt(this.state.pos);if(i===e)break;38===i?(t+=this.input.slice(s,this.state.pos),t+=this.jsxReadEntity(),s=this.state.pos):eu(i)?(t+=this.input.slice(s,this.state.pos),t+=this.jsxReadNewLine(!1),s=this.state.pos):++this.state.pos}t+=this.input.slice(s,this.state.pos++),this.finishToken(134,t)}jsxReadEntity(){let e=++this.state.pos;if(35===this.codePointAtPos(this.state.pos)){++this.state.pos;let e=10;120===this.codePointAtPos(this.state.pos)&&(e=16,++this.state.pos);let t=this.readInt(e,void 0,!1,"bail");if(null!==t&&59===this.codePointAtPos(this.state.pos))return++this.state.pos,String.fromCodePoint(t)}else{let t=0,s=!1;for(;t++<10&&this.state.pos<this.length&&!(s=59===this.codePointAtPos(this.state.pos));)++this.state.pos;if(s){let t=e$[this.input.slice(e,this.state.pos)];if(++this.state.pos,t)return t}}return this.state.pos=e,"&"}jsxReadWord(){let e,t=this.state.pos;do e=this.input.charCodeAt(++this.state.pos);while(J(e)||45===e);this.finishToken(141,this.input.slice(t,this.state.pos))}jsxParseIdentifier(){let e=this.startNode();return this.match(141)?e.name=this.state.value:R(this.state.type)?e.name=I[this.state.type]:this.unexpected(),this.next(),this.finishNode(e,"JSXIdentifier")}jsxParseNamespacedName(){let e=this.state.startLoc,t=this.jsxParseIdentifier();if(!this.eat(14))return t;let s=this.startNodeAt(e);return s.namespace=t,s.name=this.jsxParseIdentifier(),this.finishNode(s,"JSXNamespacedName")}jsxParseElementName(){let e=this.state.startLoc,t=this.jsxParseNamespacedName();if("JSXNamespacedName"===t.type)return t;for(;this.eat(16);){let s=this.startNodeAt(e);s.object=t,s.property=this.jsxParseIdentifier(),t=this.finishNode(s,"JSXMemberExpression")}return t}jsxParseAttributeValue(){let e;switch(this.state.type){case 5:return e=this.startNode(),this.setContext(P.brace),this.next(),"JSXEmptyExpression"===(e=this.jsxParseExpressionContainer(e,P.j_oTag)).expression.type&&this.raise(eK.AttributeIsEmpty,e),e;case 143:case 134:return this.parseExprAtom();default:throw this.raise(eK.UnsupportedJsxValue,this.state.startLoc)}}jsxParseEmptyExpression(){let e=this.startNodeAt(this.state.lastTokEndLoc);return this.finishNodeAt(e,"JSXEmptyExpression",this.state.startLoc)}jsxParseSpreadChild(e){return this.next(),e.expression=this.parseExpression(),this.setContext(P.j_expr),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXSpreadChild")}jsxParseExpressionContainer(e,t){return this.match(8)?e.expression=this.jsxParseEmptyExpression():e.expression=this.parseExpression(),this.setContext(t),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXExpressionContainer")}jsxParseAttribute(){let e=this.startNode();return this.match(5)?(this.setContext(P.brace),this.next(),this.expect(21),e.argument=this.parseMaybeAssignAllowIn(),this.setContext(P.j_oTag),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXSpreadAttribute")):(e.name=this.jsxParseNamespacedName(),e.value=this.eat(29)?this.jsxParseAttributeValue():null,this.finishNode(e,"JSXAttribute"))}jsxParseOpeningElementAt(e){let t=this.startNodeAt(e);return this.eat(144)?this.finishNode(t,"JSXOpeningFragment"):(t.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(t))}jsxParseOpeningElementAfterName(e){let t=[];for(;!this.match(56)&&!this.match(144);)t.push(this.jsxParseAttribute());return e.attributes=t,e.selfClosing=this.eat(56),this.expect(144),this.finishNode(e,"JSXOpeningElement")}jsxParseClosingElementAt(e){let t=this.startNodeAt(e);return this.eat(144)?this.finishNode(t,"JSXClosingFragment"):(t.name=this.jsxParseElementName(),this.expect(144),this.finishNode(t,"JSXClosingElement"))}jsxParseElementAt(e){let t=this.startNodeAt(e),s=[],i=this.jsxParseOpeningElementAt(e),r=null;if(!i.selfClosing){t:for(;;)switch(this.state.type){case 143:if(e=this.state.startLoc,this.next(),this.eat(56)){r=this.jsxParseClosingElementAt(e);break t}s.push(this.jsxParseElementAt(e));break;case 142:s.push(this.parseLiteral(this.state.value,"JSXText"));break;case 5:{let e=this.startNode();this.setContext(P.brace),this.next(),this.match(21)?s.push(this.jsxParseSpreadChild(e)):s.push(this.jsxParseExpressionContainer(e,P.j_expr));break}default:this.unexpected()}eJ(i)&&!eJ(r)&&null!==r?this.raise(eK.MissingClosingTagFragment,r):!eJ(i)&&eJ(r)?this.raise(eK.MissingClosingTagElement,r,{openingTagName:eW(i.name)}):eJ(i)||eJ(r)||eW(r.name)===eW(i.name)||this.raise(eK.MissingClosingTagElement,r,{openingTagName:eW(i.name)})}if(eJ(i)?(t.openingFragment=i,t.closingFragment=r):(t.openingElement=i,t.closingElement=r),t.children=s,this.match(47))throw this.raise(eK.UnwrappedAdjacentJSXElements,this.state.startLoc);return eJ(i)?this.finishNode(t,"JSXFragment"):this.finishNode(t,"JSXElement")}jsxParseElement(){let e=this.state.startLoc;return this.next(),this.jsxParseElementAt(e)}setContext(e){let{context:t}=this.state;t[t.length-1]=e}parseExprAtom(e){return this.match(143)?this.jsxParseElement():this.match(47)&&33!==this.input.charCodeAt(this.state.pos)?(this.replaceToken(143),this.jsxParseElement()):super.parseExprAtom(e)}skipSpace(){this.curContext().preserveSpace||super.skipSpace()}getTokenFromCode(e){let t=this.curContext();if(t===P.j_expr)return void this.jsxReadToken();if(t===P.j_oTag||t===P.j_cTag){if(K(e))return void this.jsxReadWord();if(62===e){++this.state.pos,this.finishToken(144);return}if((34===e||39===e)&&t===P.j_oTag)return void this.jsxReadString(e)}if(60===e&&this.state.canStartJSXElement&&33!==this.input.charCodeAt(this.state.pos+1)){++this.state.pos,this.finishToken(143);return}super.getTokenFromCode(e)}updateContext(e){let{context:t,type:s}=this.state;if(56===s&&143===e)t.splice(-2,2,P.j_cTag),this.state.canStartJSXElement=!1;else if(143===s)t.push(P.j_oTag);else if(144===s){let s=t[t.length-1];s===P.j_oTag&&56===e||s===P.j_cTag?(t.pop(),this.state.canStartJSXElement=t[t.length-1]===P.j_expr):(this.setContext(P.j_expr),this.state.canStartJSXElement=!0)}else this.state.canStartJSXElement=w[s]}},flow:e=>class extends e{constructor(...e){super(...e),this.flowPragma=void 0}getScopeHandler(){return ea}shouldParseTypes(){return this.getPluginOption("flow","all")||"flow"===this.flowPragma}finishToken(e,t){134!==e&&13!==e&&28!==e&&void 0===this.flowPragma&&(this.flowPragma=null),super.finishToken(e,t)}addComment(e){if(void 0===this.flowPragma){let t=eq.exec(e.value);if(t)if("flow"===t[1])this.flowPragma="flow";else if("noflow"===t[1])this.flowPragma="noflow";else throw Error("Unexpected flow pragma")}super.addComment(e)}flowParseTypeInitialiser(e){let t=this.state.inType;this.state.inType=!0,this.expect(e||14);let s=this.flowParseType();return this.state.inType=t,s}flowParsePredicate(){let e=this.startNode(),t=this.state.startLoc;return(this.next(),this.expectContextual(110),this.state.lastTokStartLoc.index>t.index+1&&this.raise(eH.UnexpectedSpaceBetweenModuloChecks,t),this.eat(10))?(e.value=super.parseExpression(),this.expect(11),this.finishNode(e,"DeclaredPredicate")):this.finishNode(e,"InferredPredicate")}flowParseTypeAndPredicateInitialiser(){let e=this.state.inType;this.state.inType=!0,this.expect(14);let t=null,s=null;return this.match(54)?(this.state.inType=e,s=this.flowParsePredicate()):(t=this.flowParseType(),this.state.inType=e,this.match(54)&&(s=this.flowParsePredicate())),[t,s]}flowParseDeclareClass(e){return this.next(),this.flowParseInterfaceish(e,!0),this.finishNode(e,"DeclareClass")}flowParseDeclareFunction(e){this.next();let t=e.id=this.parseIdentifier(),s=this.startNode(),i=this.startNode();this.match(47)?s.typeParameters=this.flowParseTypeParameterDeclaration():s.typeParameters=null,this.expect(10);let r=this.flowParseFunctionTypeParams();return s.params=r.params,s.rest=r.rest,s.this=r._this,this.expect(11),[s.returnType,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),i.typeAnnotation=this.finishNode(s,"FunctionTypeAnnotation"),t.typeAnnotation=this.finishNode(i,"TypeAnnotation"),this.resetEndLocation(t),this.semicolon(),this.scope.declareName(e.id.name,2048,e.id.loc.start),this.finishNode(e,"DeclareFunction")}flowParseDeclare(e,t){if(this.match(80))return this.flowParseDeclareClass(e);if(this.match(68))return this.flowParseDeclareFunction(e);if(this.match(74))return this.flowParseDeclareVariable(e);if(this.eatContextual(127))if(this.match(16))return this.flowParseDeclareModuleExports(e);else return t&&this.raise(eH.NestedDeclareModule,this.state.lastTokStartLoc),this.flowParseDeclareModule(e);if(this.isContextual(130))return this.flowParseDeclareTypeAlias(e);if(this.isContextual(131))return this.flowParseDeclareOpaqueType(e);if(this.isContextual(129))return this.flowParseDeclareInterface(e);if(this.match(82))return this.flowParseDeclareExportDeclaration(e,t);this.unexpected()}flowParseDeclareVariable(e){return this.next(),e.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(e.id.name,5,e.id.loc.start),this.semicolon(),this.finishNode(e,"DeclareVariable")}flowParseDeclareModule(e){this.scope.enter(0),this.match(134)?e.id=super.parseExprAtom():e.id=this.parseIdentifier();let t=e.body=this.startNode(),s=t.body=[];for(this.expect(5);!this.match(8);){let e=this.startNode();this.match(83)?(this.next(),this.isContextual(130)||this.match(87)||this.raise(eH.InvalidNonTypeImportInDeclareModule,this.state.lastTokStartLoc),super.parseImport(e)):(this.expectContextual(125,eH.UnsupportedStatementInDeclareModule),e=this.flowParseDeclare(e,!0)),s.push(e)}this.scope.exit(),this.expect(8),this.finishNode(t,"BlockStatement");let i=null,r=!1;return s.forEach(e=>{"DeclareExportAllDeclaration"!==e.type&&("DeclareExportDeclaration"!==e.type||e.declaration&&("TypeAlias"===e.declaration.type||"InterfaceDeclaration"===e.declaration.type))?"DeclareModuleExports"===e.type&&(r&&this.raise(eH.DuplicateDeclareModuleExports,e),"ES"===i&&this.raise(eH.AmbiguousDeclareModuleKind,e),i="CommonJS",r=!0):("CommonJS"===i&&this.raise(eH.AmbiguousDeclareModuleKind,e),i="ES")}),e.kind=i||"CommonJS",this.finishNode(e,"DeclareModule")}flowParseDeclareExportDeclaration(e,t){if(this.expect(82),this.eat(65))return this.match(68)||this.match(80)?e.declaration=this.flowParseDeclare(this.startNode()):(e.declaration=this.flowParseType(),this.semicolon()),e.default=!0,this.finishNode(e,"DeclareExportDeclaration");if(this.match(75)||this.isLet()||(this.isContextual(130)||this.isContextual(129))&&!t){let e=this.state.value;throw this.raise(eH.UnsupportedDeclareExportKind,this.state.startLoc,{unsupportedExportKind:e,suggestion:eV[e]})}return this.match(74)||this.match(68)||this.match(80)||this.isContextual(131)?(e.declaration=this.flowParseDeclare(this.startNode()),e.default=!1,this.finishNode(e,"DeclareExportDeclaration")):this.match(55)||this.match(5)||this.isContextual(129)||this.isContextual(130)||this.isContextual(131)?("ExportNamedDeclaration"===(e=this.parseExport(e,null)).type&&(e.type="ExportDeclaration",e.default=!1,delete e.exportKind),e.type="Declare"+e.type,e):void this.unexpected()}flowParseDeclareModuleExports(e){return this.next(),this.expectContextual(111),e.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(e,"DeclareModuleExports")}flowParseDeclareTypeAlias(e){this.next();let t=this.flowParseTypeAlias(e);return t.type="DeclareTypeAlias",t}flowParseDeclareOpaqueType(e){this.next();let t=this.flowParseOpaqueType(e,!0);return t.type="DeclareOpaqueType",t}flowParseDeclareInterface(e){return this.next(),this.flowParseInterfaceish(e,!1),this.finishNode(e,"DeclareInterface")}flowParseInterfaceish(e,t){if(e.id=this.flowParseRestrictedIdentifier(!t,!0),this.scope.declareName(e.id.name,t?17:8201,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.extends=[],this.eat(81))do e.extends.push(this.flowParseInterfaceExtends());while(!t&&this.eat(12));if(t){if(e.implements=[],e.mixins=[],this.eatContextual(117))do e.mixins.push(this.flowParseInterfaceExtends());while(this.eat(12));if(this.eatContextual(113))do e.implements.push(this.flowParseInterfaceExtends());while(this.eat(12))}e.body=this.flowParseObjectType({allowStatic:t,allowExact:!1,allowSpread:!1,allowProto:t,allowInexact:!1})}flowParseInterfaceExtends(){let e=this.startNode();return e.id=this.flowParseQualifiedTypeIdentifier(),this.match(47)?e.typeParameters=this.flowParseTypeParameterInstantiation():e.typeParameters=null,this.finishNode(e,"InterfaceExtends")}flowParseInterface(e){return this.flowParseInterfaceish(e,!1),this.finishNode(e,"InterfaceDeclaration")}checkNotUnderscore(e){"_"===e&&this.raise(eH.UnexpectedReservedUnderscore,this.state.startLoc)}checkReservedType(e,t,s){e_.has(e)&&this.raise(s?eH.AssignReservedType:eH.UnexpectedReservedType,t,{reservedType:e})}flowParseRestrictedIdentifier(e,t){return this.checkReservedType(this.state.value,this.state.startLoc,t),this.parseIdentifier(e)}flowParseTypeAlias(e){return e.id=this.flowParseRestrictedIdentifier(!1,!0),this.scope.declareName(e.id.name,8201,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.right=this.flowParseTypeInitialiser(29),this.semicolon(),this.finishNode(e,"TypeAlias")}flowParseOpaqueType(e,t){return this.expectContextual(130),e.id=this.flowParseRestrictedIdentifier(!0,!0),this.scope.declareName(e.id.name,8201,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.supertype=null,this.match(14)&&(e.supertype=this.flowParseTypeInitialiser(14)),e.impltype=null,t||(e.impltype=this.flowParseTypeInitialiser(29)),this.semicolon(),this.finishNode(e,"OpaqueType")}flowParseTypeParameter(e=!1){let t=this.state.startLoc,s=this.startNode(),i=this.flowParseVariance(),r=this.flowParseTypeAnnotatableIdentifier();return s.name=r.name,s.variance=i,s.bound=r.typeAnnotation,this.match(29)?(this.eat(29),s.default=this.flowParseType()):e&&this.raise(eH.MissingTypeParamDefault,t),this.finishNode(s,"TypeParameter")}flowParseTypeParameterDeclaration(){let e=this.state.inType,t=this.startNode();t.params=[],this.state.inType=!0,this.match(47)||this.match(143)?this.next():this.unexpected();let s=!1;do{let e=this.flowParseTypeParameter(s);t.params.push(e),e.default&&(s=!0),this.match(48)||this.expect(12)}while(!this.match(48));return this.expect(48),this.state.inType=e,this.finishNode(t,"TypeParameterDeclaration")}flowInTopLevelContext(e){if(this.curContext()===P.brace)return e();{let t=this.state.context;this.state.context=[t[0]];try{return e()}finally{this.state.context=t}}}flowParseTypeParameterInstantiationInExpression(){if(47===this.reScan_lt())return this.flowParseTypeParameterInstantiation()}flowParseTypeParameterInstantiation(){let e=this.startNode(),t=this.state.inType;return this.state.inType=!0,e.params=[],this.flowInTopLevelContext(()=>{this.expect(47);let t=this.state.noAnonFunctionType;for(this.state.noAnonFunctionType=!1;!this.match(48);)e.params.push(this.flowParseType()),this.match(48)||this.expect(12);this.state.noAnonFunctionType=t}),this.state.inType=t,this.state.inType||this.curContext()!==P.brace||this.reScan_lt_gt(),this.expect(48),this.finishNode(e,"TypeParameterInstantiation")}flowParseTypeParameterInstantiationCallOrNew(){if(47!==this.reScan_lt())return;let e=this.startNode(),t=this.state.inType;for(e.params=[],this.state.inType=!0,this.expect(47);!this.match(48);)e.params.push(this.flowParseTypeOrImplicitInstantiation()),this.match(48)||this.expect(12);return this.expect(48),this.state.inType=t,this.finishNode(e,"TypeParameterInstantiation")}flowParseInterfaceType(){let e=this.startNode();if(this.expectContextual(129),e.extends=[],this.eat(81))do e.extends.push(this.flowParseInterfaceExtends());while(this.eat(12));return e.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(e,"InterfaceTypeAnnotation")}flowParseObjectPropertyKey(){return this.match(135)||this.match(134)?super.parseExprAtom():this.parseIdentifier(!0)}flowParseObjectTypeIndexer(e,t,s){return e.static=t,14===this.lookahead().type?(e.id=this.flowParseObjectPropertyKey(),e.key=this.flowParseTypeInitialiser()):(e.id=null,e.key=this.flowParseType()),this.expect(3),e.value=this.flowParseTypeInitialiser(),e.variance=s,this.finishNode(e,"ObjectTypeIndexer")}flowParseObjectTypeInternalSlot(e,t){return e.static=t,e.id=this.flowParseObjectPropertyKey(),this.expect(3),this.expect(3),this.match(47)||this.match(10)?(e.method=!0,e.optional=!1,e.value=this.flowParseObjectTypeMethodish(this.startNodeAt(e.loc.start))):(e.method=!1,this.eat(17)&&(e.optional=!0),e.value=this.flowParseTypeInitialiser()),this.finishNode(e,"ObjectTypeInternalSlot")}flowParseObjectTypeMethodish(e){for(e.params=[],e.rest=null,e.typeParameters=null,e.this=null,this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(10),this.match(78)&&(e.this=this.flowParseFunctionTypeParam(!0),e.this.name=null,this.match(11)||this.expect(12));!this.match(11)&&!this.match(21);)e.params.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(e.rest=this.flowParseFunctionTypeParam(!1)),this.expect(11),e.returnType=this.flowParseTypeInitialiser(),this.finishNode(e,"FunctionTypeAnnotation")}flowParseObjectTypeCallProperty(e,t){let s=this.startNode();return e.static=t,e.value=this.flowParseObjectTypeMethodish(s),this.finishNode(e,"ObjectTypeCallProperty")}flowParseObjectType({allowStatic:e,allowExact:t,allowSpread:s,allowProto:i,allowInexact:r}){let a,n,o=this.state.inType;this.state.inType=!0;let h=this.startNode();h.callProperties=[],h.properties=[],h.indexers=[],h.internalSlots=[];let l=!1;for(t&&this.match(6)?(this.expect(6),a=9,n=!0):(this.expect(5),a=8,n=!1),h.exact=n;!this.match(a);){let t=!1,a=null,o=null,p=this.startNode();if(i&&this.isContextual(118)){let t=this.lookahead();14!==t.type&&17!==t.type&&(this.next(),a=this.state.startLoc,e=!1)}if(e&&this.isContextual(106)){let e=this.lookahead();14!==e.type&&17!==e.type&&(this.next(),t=!0)}let c=this.flowParseVariance();if(this.eat(0))null!=a&&this.unexpected(a),this.eat(0)?(c&&this.unexpected(c.loc.start),h.internalSlots.push(this.flowParseObjectTypeInternalSlot(p,t))):h.indexers.push(this.flowParseObjectTypeIndexer(p,t,c));else if(this.match(10)||this.match(47))null!=a&&this.unexpected(a),c&&this.unexpected(c.loc.start),h.callProperties.push(this.flowParseObjectTypeCallProperty(p,t));else{let e="init";(this.isContextual(99)||this.isContextual(104))&&F(this.lookahead().type)&&(e=this.state.value,this.next());let i=this.flowParseObjectTypeProperty(p,t,a,c,e,s,null!=r?r:!n);null===i?(l=!0,o=this.state.lastTokStartLoc):h.properties.push(i)}this.flowObjectTypeSemicolon(),!o||this.match(8)||this.match(9)||this.raise(eH.UnexpectedExplicitInexactInObject,o)}this.expect(a),s&&(h.inexact=l);let p=this.finishNode(h,"ObjectTypeAnnotation");return this.state.inType=o,p}flowParseObjectTypeProperty(e,t,s,i,r,a,n){if(this.eat(21))return this.match(12)||this.match(13)||this.match(8)||this.match(9)?(a?n||this.raise(eH.InexactInsideExact,this.state.lastTokStartLoc):this.raise(eH.InexactInsideNonObject,this.state.lastTokStartLoc),i&&this.raise(eH.InexactVariance,i),null):(a||this.raise(eH.UnexpectedSpreadType,this.state.lastTokStartLoc),null!=s&&this.unexpected(s),i&&this.raise(eH.SpreadVariance,i),e.argument=this.flowParseType(),this.finishNode(e,"ObjectTypeSpreadProperty"));{e.key=this.flowParseObjectPropertyKey(),e.static=t,e.proto=null!=s,e.kind=r;let n=!1;return this.match(47)||this.match(10)?(e.method=!0,null!=s&&this.unexpected(s),i&&this.unexpected(i.loc.start),e.value=this.flowParseObjectTypeMethodish(this.startNodeAt(e.loc.start)),("get"===r||"set"===r)&&this.flowCheckGetterSetterParams(e),!a&&"constructor"===e.key.name&&e.value.this&&this.raise(eH.ThisParamBannedInConstructor,e.value.this)):("init"!==r&&this.unexpected(),e.method=!1,this.eat(17)&&(n=!0),e.value=this.flowParseTypeInitialiser(),e.variance=i),e.optional=n,this.finishNode(e,"ObjectTypeProperty")}}flowCheckGetterSetterParams(e){let t=+("get"!==e.kind),s=e.value.params.length+ +!!e.value.rest;e.value.this&&this.raise("get"===e.kind?eH.GetterMayNotHaveThisParam:eH.SetterMayNotHaveThisParam,e.value.this),s!==t&&this.raise("get"===e.kind?d.BadGetterArity:d.BadSetterArity,e),"set"===e.kind&&e.value.rest&&this.raise(d.BadSetterRestParameter,e)}flowObjectTypeSemicolon(){this.eat(13)||this.eat(12)||this.match(8)||this.match(9)||this.unexpected()}flowParseQualifiedTypeIdentifier(e,t){null!=e||(e=this.state.startLoc);let s=t||this.flowParseRestrictedIdentifier(!0);for(;this.eat(16);){let t=this.startNodeAt(e);t.qualification=s,t.id=this.flowParseRestrictedIdentifier(!0),s=this.finishNode(t,"QualifiedTypeIdentifier")}return s}flowParseGenericType(e,t){let s=this.startNodeAt(e);return s.typeParameters=null,s.id=this.flowParseQualifiedTypeIdentifier(e,t),this.match(47)&&(s.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(s,"GenericTypeAnnotation")}flowParseTypeofType(){let e=this.startNode();return this.expect(87),e.argument=this.flowParsePrimaryType(),this.finishNode(e,"TypeofTypeAnnotation")}flowParseTupleType(){let e=this.startNode();for(e.types=[],this.expect(0);this.state.pos<this.length&&!this.match(3)&&(e.types.push(this.flowParseType()),!this.match(3));)this.expect(12);return this.expect(3),this.finishNode(e,"TupleTypeAnnotation")}flowParseFunctionTypeParam(e){let t=null,s=!1,i=null,r=this.startNode(),a=this.lookahead(),n=78===this.state.type;return 14===a.type||17===a.type?(n&&!e&&this.raise(eH.ThisParamMustBeFirst,r),t=this.parseIdentifier(n),this.eat(17)&&(s=!0,n&&this.raise(eH.ThisParamMayNotBeOptional,r)),i=this.flowParseTypeInitialiser()):i=this.flowParseType(),r.name=t,r.optional=s,r.typeAnnotation=i,this.finishNode(r,"FunctionTypeParam")}reinterpretTypeAsFunctionTypeParam(e){let t=this.startNodeAt(e.loc.start);return t.name=null,t.optional=!1,t.typeAnnotation=e,this.finishNode(t,"FunctionTypeParam")}flowParseFunctionTypeParams(e=[]){let t=null,s=null;for(this.match(78)&&((s=this.flowParseFunctionTypeParam(!0)).name=null,this.match(11)||this.expect(12));!this.match(11)&&!this.match(21);)e.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(t=this.flowParseFunctionTypeParam(!1)),{params:e,rest:t,_this:s}}flowIdentToTypeAnnotation(e,t,s){switch(s.name){case"any":return this.finishNode(t,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(t,"BooleanTypeAnnotation");case"mixed":return this.finishNode(t,"MixedTypeAnnotation");case"empty":return this.finishNode(t,"EmptyTypeAnnotation");case"number":return this.finishNode(t,"NumberTypeAnnotation");case"string":return this.finishNode(t,"StringTypeAnnotation");case"symbol":return this.finishNode(t,"SymbolTypeAnnotation");default:return this.checkNotUnderscore(s.name),this.flowParseGenericType(e,s)}}flowParsePrimaryType(){let e,t,s=this.state.startLoc,i=this.startNode(),r=!1,a=this.state.noAnonFunctionType;switch(this.state.type){case 5:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case 6:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case 0:return this.state.noAnonFunctionType=!1,t=this.flowParseTupleType(),this.state.noAnonFunctionType=a,t;case 47:{let t=this.startNode();return t.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(10),t.params=(e=this.flowParseFunctionTypeParams()).params,t.rest=e.rest,t.this=e._this,this.expect(11),this.expect(19),t.returnType=this.flowParseType(),this.finishNode(t,"FunctionTypeAnnotation")}case 10:{let s=this.startNode();if(this.next(),!this.match(11)&&!this.match(21))if(D(this.state.type)||this.match(78)){let e=this.lookahead().type;r=17!==e&&14!==e}else r=!0;if(r){if(this.state.noAnonFunctionType=!1,t=this.flowParseType(),this.state.noAnonFunctionType=a,this.state.noAnonFunctionType||!(this.match(12)||this.match(11)&&19===this.lookahead().type))return this.expect(11),t;this.eat(12)}return s.params=(e=t?this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(t)]):this.flowParseFunctionTypeParams()).params,s.rest=e.rest,s.this=e._this,this.expect(11),this.expect(19),s.returnType=this.flowParseType(),s.typeParameters=null,this.finishNode(s,"FunctionTypeAnnotation")}case 134:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case 85:case 86:return i.value=this.match(85),this.next(),this.finishNode(i,"BooleanLiteralTypeAnnotation");case 53:if("-"===this.state.value){if(this.next(),this.match(135))return this.parseLiteralAtNode(-this.state.value,"NumberLiteralTypeAnnotation",i);if(this.match(136))return this.parseLiteralAtNode(-this.state.value,"BigIntLiteralTypeAnnotation",i);throw this.raise(eH.UnexpectedSubtractionOperand,this.state.startLoc)}this.unexpected();return;case 135:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case 136:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case 88:return this.next(),this.finishNode(i,"VoidTypeAnnotation");case 84:return this.next(),this.finishNode(i,"NullLiteralTypeAnnotation");case 78:return this.next(),this.finishNode(i,"ThisTypeAnnotation");case 55:return this.next(),this.finishNode(i,"ExistsTypeAnnotation");case 87:return this.flowParseTypeofType();default:if(R(this.state.type)){let e=I[this.state.type];return this.next(),super.createIdentifier(i,e)}if(D(this.state.type)){if(this.isContextual(129))return this.flowParseInterfaceType();return this.flowIdentToTypeAnnotation(s,i,this.parseIdentifier())}}this.unexpected()}flowParsePostfixType(){let e=this.state.startLoc,t=this.flowParsePrimaryType(),s=!1;for(;(this.match(0)||this.match(18))&&!this.canInsertSemicolon();){let i=this.startNodeAt(e),r=this.eat(18);s=s||r,this.expect(0),!r&&this.match(3)?(i.elementType=t,this.next(),t=this.finishNode(i,"ArrayTypeAnnotation")):(i.objectType=t,i.indexType=this.flowParseType(),this.expect(3),s?(i.optional=r,t=this.finishNode(i,"OptionalIndexedAccessType")):t=this.finishNode(i,"IndexedAccessType"))}return t}flowParsePrefixType(){let e=this.startNode();return this.eat(17)?(e.typeAnnotation=this.flowParsePrefixType(),this.finishNode(e,"NullableTypeAnnotation")):this.flowParsePostfixType()}flowParseAnonFunctionWithoutParens(){let e=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(19)){let t=this.startNodeAt(e.loc.start);return t.params=[this.reinterpretTypeAsFunctionTypeParam(e)],t.rest=null,t.this=null,t.returnType=this.flowParseType(),t.typeParameters=null,this.finishNode(t,"FunctionTypeAnnotation")}return e}flowParseIntersectionType(){let e=this.startNode();this.eat(45);let t=this.flowParseAnonFunctionWithoutParens();for(e.types=[t];this.eat(45);)e.types.push(this.flowParseAnonFunctionWithoutParens());return 1===e.types.length?t:this.finishNode(e,"IntersectionTypeAnnotation")}flowParseUnionType(){let e=this.startNode();this.eat(43);let t=this.flowParseIntersectionType();for(e.types=[t];this.eat(43);)e.types.push(this.flowParseIntersectionType());return 1===e.types.length?t:this.finishNode(e,"UnionTypeAnnotation")}flowParseType(){let e=this.state.inType;this.state.inType=!0;let t=this.flowParseUnionType();return this.state.inType=e,t}flowParseTypeOrImplicitInstantiation(){if(132!==this.state.type||"_"!==this.state.value)return this.flowParseType();{let e=this.state.startLoc,t=this.parseIdentifier();return this.flowParseGenericType(e,t)}}flowParseTypeAnnotation(){let e=this.startNode();return e.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(e,"TypeAnnotation")}flowParseTypeAnnotatableIdentifier(e){let t=e?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(14)&&(t.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(t)),t}typeCastToParameter(e){return e.expression.typeAnnotation=e.typeAnnotation,this.resetEndLocation(e.expression,e.typeAnnotation.loc.end),e.expression}flowParseVariance(){let e=null;return this.match(53)?(e=this.startNode(),"+"===this.state.value?e.kind="plus":e.kind="minus",this.next(),this.finishNode(e,"Variance")):e}parseFunctionBody(e,t,s=!1){if(t)return void this.forwardNoArrowParamsConversionAt(e,()=>super.parseFunctionBody(e,!0,s));super.parseFunctionBody(e,!1,s)}parseFunctionBodyAndFinish(e,t,s=!1){if(this.match(14)){let t=this.startNode();[t.typeAnnotation,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),e.returnType=t.typeAnnotation?this.finishNode(t,"TypeAnnotation"):null}return super.parseFunctionBodyAndFinish(e,t,s)}parseStatementLike(e){if(this.state.strict&&this.isContextual(129)){if(O(this.lookahead().type)){let e=this.startNode();return this.next(),this.flowParseInterface(e)}}else if(this.isContextual(126)){let e=this.startNode();return this.next(),this.flowParseEnumDeclaration(e)}let t=super.parseStatementLike(e);return void 0!==this.flowPragma||this.isValidDirective(t)||(this.flowPragma=null),t}parseExpressionStatement(e,t,s){if("Identifier"===t.type){if("declare"===t.name){if(this.match(80)||D(this.state.type)||this.match(68)||this.match(74)||this.match(82))return this.flowParseDeclare(e)}else if(D(this.state.type)){if("interface"===t.name)return this.flowParseInterface(e);else if("type"===t.name)return this.flowParseTypeAlias(e);else if("opaque"===t.name)return this.flowParseOpaqueType(e,!1)}}return super.parseExpressionStatement(e,t,s)}shouldParseExportDeclaration(){let{type:e}=this.state;return 126===e||B(e)?!this.state.containsEsc:super.shouldParseExportDeclaration()}isExportDefaultSpecifier(){let{type:e}=this.state;return 126===e||B(e)?this.state.containsEsc:super.isExportDefaultSpecifier()}parseExportDefaultExpression(){if(this.isContextual(126)){let e=this.startNode();return this.next(),this.flowParseEnumDeclaration(e)}return super.parseExportDefaultExpression()}parseConditional(e,t,s){if(!this.match(17))return e;if(this.state.maybeInArrowParameters){let t=this.lookaheadCharCode();if(44===t||61===t||58===t||41===t)return this.setOptionalParametersError(s),e}this.expect(17);let i=this.state.clone(),r=this.state.noArrowAt,a=this.startNodeAt(t),{consequent:n,failed:o}=this.tryParseConditionalConsequent(),[h,l]=this.getArrowLikeExpressions(n);if(o||l.length>0){let e=[...r];if(l.length>0){this.state=i,this.state.noArrowAt=e;for(let t=0;t<l.length;t++)e.push(l[t].start);({consequent:n,failed:o}=this.tryParseConditionalConsequent()),[h,l]=this.getArrowLikeExpressions(n)}o&&h.length>1&&this.raise(eH.AmbiguousConditionalArrow,i.startLoc),o&&1===h.length&&(this.state=i,e.push(h[0].start),this.state.noArrowAt=e,{consequent:n,failed:o}=this.tryParseConditionalConsequent())}return this.getArrowLikeExpressions(n,!0),this.state.noArrowAt=r,this.expect(14),a.test=e,a.consequent=n,a.alternate=this.forwardNoArrowParamsConversionAt(a,()=>this.parseMaybeAssign(void 0,void 0)),this.finishNode(a,"ConditionalExpression")}tryParseConditionalConsequent(){this.state.noArrowParamsConversionAt.push(this.state.start);let e=this.parseMaybeAssignAllowIn(),t=!this.match(14);return this.state.noArrowParamsConversionAt.pop(),{consequent:e,failed:t}}getArrowLikeExpressions(e,t){let s=[e],i=[];for(;0!==s.length;){let e=s.pop();"ArrowFunctionExpression"===e.type&&"BlockStatement"!==e.body.type?(e.typeParameters||!e.returnType?this.finishArrowValidation(e):i.push(e),s.push(e.body)):"ConditionalExpression"===e.type&&(s.push(e.consequent),s.push(e.alternate))}return t?(i.forEach(e=>this.finishArrowValidation(e)),[i,[]]):function(e,t){let s=[],i=[];for(let r=0;r<e.length;r++)(t(e[r],r,e)?s:i).push(e[r]);return[s,i]}(i,e=>e.params.every(e=>this.isAssignable(e,!0)))}finishArrowValidation(e){var t;this.toAssignableList(e.params,null==(t=e.extra)?void 0:t.trailingCommaLoc,!1),this.scope.enter(6),super.checkParams(e,!1,!0),this.scope.exit()}forwardNoArrowParamsConversionAt(e,t){let s;return this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(e.start))?(this.state.noArrowParamsConversionAt.push(this.state.start),s=t(),this.state.noArrowParamsConversionAt.pop()):s=t(),s}parseParenItem(e,t){let s=super.parseParenItem(e,t);if(this.eat(17)&&(s.optional=!0,this.resetEndLocation(e)),this.match(14)){let e=this.startNodeAt(t);return e.expression=s,e.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(e,"TypeCastExpression")}return s}assertModuleNodeAllowed(e){("ImportDeclaration"!==e.type||"type"!==e.importKind&&"typeof"!==e.importKind)&&("ExportNamedDeclaration"!==e.type||"type"!==e.exportKind)&&("ExportAllDeclaration"!==e.type||"type"!==e.exportKind)&&super.assertModuleNodeAllowed(e)}parseExportDeclaration(e){if(this.isContextual(130)){e.exportKind="type";let t=this.startNode();return(this.next(),this.match(5))?(e.specifiers=this.parseExportSpecifiers(!0),super.parseExportFrom(e),null):this.flowParseTypeAlias(t)}if(this.isContextual(131)){e.exportKind="type";let t=this.startNode();return this.next(),this.flowParseOpaqueType(t,!1)}if(this.isContextual(129)){e.exportKind="type";let t=this.startNode();return this.next(),this.flowParseInterface(t)}{if(!this.isContextual(126))return super.parseExportDeclaration(e);e.exportKind="value";let t=this.startNode();return this.next(),this.flowParseEnumDeclaration(t)}}eatExportStar(e){return!!super.eatExportStar(e)||!!this.isContextual(130)&&55===this.lookahead().type&&(e.exportKind="type",this.next(),this.next(),!0)}maybeParseExportNamespaceSpecifier(e){let{startLoc:t}=this.state,s=super.maybeParseExportNamespaceSpecifier(e);return s&&"type"===e.exportKind&&this.unexpected(t),s}parseClassId(e,t,s){super.parseClassId(e,t,s),this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration())}parseClassMember(e,t,s){let{startLoc:i}=this.state;if(this.isContextual(125)){if(super.parseClassMemberFromModifier(e,t))return;t.declare=!0}super.parseClassMember(e,t,s),t.declare&&("ClassProperty"!==t.type&&"ClassPrivateProperty"!==t.type&&"PropertyDefinition"!==t.type?this.raise(eH.DeclareClassElement,i):t.value&&this.raise(eH.DeclareClassFieldInitializer,t.value))}isIterator(e){return"iterator"===e||"asyncIterator"===e}readIterator(){let e=super.readWord1(),t="@@"+e;this.isIterator(e)&&this.state.inType||this.raise(d.InvalidIdentifier,this.state.curPosition(),{identifierName:t}),this.finishToken(132,t)}getTokenFromCode(e){let t=this.input.charCodeAt(this.state.pos+1);if(123===e&&124===t)this.finishOp(6,2);else if(this.state.inType&&(62===e||60===e))this.finishOp(62===e?48:47,1);else if(this.state.inType&&63===e)46===t?this.finishOp(18,2):this.finishOp(17,1);else{var s;(s=this.input.charCodeAt(this.state.pos+2),64===e&&64===t&&K(s))?(this.state.pos+=2,this.readIterator()):super.getTokenFromCode(e)}}isAssignable(e,t){return"TypeCastExpression"===e.type?this.isAssignable(e.expression,t):super.isAssignable(e,t)}toAssignable(e,t=!1){t||"AssignmentExpression"!==e.type||"TypeCastExpression"!==e.left.type||(e.left=this.typeCastToParameter(e.left)),super.toAssignable(e,t)}toAssignableList(e,t,s){for(let t=0;t<e.length;t++){let s=e[t];(null==s?void 0:s.type)==="TypeCastExpression"&&(e[t]=this.typeCastToParameter(s))}super.toAssignableList(e,t,s)}toReferencedList(e,t){for(let i=0;i<e.length;i++){var s;let r=e[i];r&&"TypeCastExpression"===r.type&&!(null!=(s=r.extra)&&s.parenthesized)&&(e.length>1||!t)&&this.raise(eH.TypeCastInPattern,r.typeAnnotation)}return e}parseArrayLike(e,t,s,i){let r=super.parseArrayLike(e,t,s,i);return t&&!this.state.maybeInArrowParameters&&this.toReferencedList(r.elements),r}isValidLVal(e,t,s){return"TypeCastExpression"===e||super.isValidLVal(e,t,s)}parseClassProperty(e){return this.match(14)&&(e.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassProperty(e)}parseClassPrivateProperty(e){return this.match(14)&&(e.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassPrivateProperty(e)}isClassMethod(){return this.match(47)||super.isClassMethod()}isClassProperty(){return this.match(14)||super.isClassProperty()}isNonstaticConstructor(e){return!this.match(14)&&super.isNonstaticConstructor(e)}pushClassMethod(e,t,s,i,r,a){if(t.variance&&this.unexpected(t.variance.loc.start),delete t.variance,this.match(47)&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassMethod(e,t,s,i,r,a),t.params&&r){let e=t.params;e.length>0&&this.isThisParam(e[0])&&this.raise(eH.ThisParamBannedInConstructor,t)}else if("MethodDefinition"===t.type&&r&&t.value.params){let e=t.value.params;e.length>0&&this.isThisParam(e[0])&&this.raise(eH.ThisParamBannedInConstructor,t)}}pushClassPrivateMethod(e,t,s,i){t.variance&&this.unexpected(t.variance.loc.start),delete t.variance,this.match(47)&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassPrivateMethod(e,t,s,i)}parseClassSuper(e){if(super.parseClassSuper(e),e.superClass&&(this.match(47)||this.match(51))&&(e.superTypeParameters=this.flowParseTypeParameterInstantiationInExpression()),this.isContextual(113)){this.next();let t=e.implements=[];do{let e=this.startNode();e.id=this.flowParseRestrictedIdentifier(!0),this.match(47)?e.typeParameters=this.flowParseTypeParameterInstantiation():e.typeParameters=null,t.push(this.finishNode(e,"ClassImplements"))}while(this.eat(12))}}checkGetterSetterParams(e){super.checkGetterSetterParams(e);let t=this.getObjectOrClassMethodParams(e);if(t.length>0){let s=t[0];this.isThisParam(s)&&"get"===e.kind?this.raise(eH.GetterMayNotHaveThisParam,s):this.isThisParam(s)&&this.raise(eH.SetterMayNotHaveThisParam,s)}}parsePropertyNamePrefixOperator(e){e.variance=this.flowParseVariance()}parseObjPropValue(e,t,s,i,r,a,n){let o;e.variance&&this.unexpected(e.variance.loc.start),delete e.variance,this.match(47)&&!a&&(o=this.flowParseTypeParameterDeclaration(),this.match(10)||this.unexpected());let h=super.parseObjPropValue(e,t,s,i,r,a,n);return o&&((h.value||h).typeParameters=o),h}parseFunctionParamType(e){return this.eat(17)&&("Identifier"!==e.type&&this.raise(eH.PatternIsOptional,e),this.isThisParam(e)&&this.raise(eH.ThisParamMayNotBeOptional,e),e.optional=!0),this.match(14)?e.typeAnnotation=this.flowParseTypeAnnotation():this.isThisParam(e)&&this.raise(eH.ThisParamAnnotationRequired,e),this.match(29)&&this.isThisParam(e)&&this.raise(eH.ThisParamNoDefault,e),this.resetEndLocation(e),e}parseMaybeDefault(e,t){let s=super.parseMaybeDefault(e,t);return"AssignmentPattern"===s.type&&s.typeAnnotation&&s.right.start<s.typeAnnotation.start&&this.raise(eH.TypeBeforeInitializer,s.typeAnnotation),s}checkImportReflection(e){super.checkImportReflection(e),e.module&&"value"!==e.importKind&&this.raise(eH.ImportReflectionHasImportType,e.specifiers[0].loc.start)}parseImportSpecifierLocal(e,t,s){t.local=ez(e)?this.flowParseRestrictedIdentifier(!0,!0):this.parseIdentifier(),e.specifiers.push(this.finishImportSpecifier(t,s))}isPotentialImportPhase(e){if(super.isPotentialImportPhase(e))return!0;if(this.isContextual(130)){if(!e)return!0;let t=this.lookaheadCharCode();return 123===t||42===t}return!e&&this.isContextual(87)}applyImportPhase(e,t,s,i){(super.applyImportPhase(e,t,s,i),t)?!s&&this.match(65)||(e.exportKind="type"===s?s:"value"):("type"===s&&this.match(55)&&this.unexpected(),e.importKind="type"===s||"typeof"===s?s:"value")}parseImportSpecifier(e,t,s,i,r){let a=e.imported,n=null;"Identifier"===a.type&&("type"===a.name?n="type":"typeof"===a.name&&(n="typeof"));let o=!1;if(this.isContextual(93)&&!this.isLookaheadContextual("as")){let t=this.parseIdentifier(!0);null===n||O(this.state.type)?(e.imported=a,e.importKind=null,e.local=this.parseIdentifier()):(e.imported=t,e.importKind=n,e.local=eU(t))}else{if(null!==n&&O(this.state.type))e.imported=this.parseIdentifier(!0),e.importKind=n;else{if(t)throw this.raise(d.ImportBindingIsString,e,{importName:a.value});e.imported=a,e.importKind=null}this.eatContextual(93)?e.local=this.parseIdentifier():(o=!0,e.local=eU(e.imported))}let h=ez(e);return s&&h&&this.raise(eH.ImportTypeShorthandOnlyInPureImport,e),(s||h)&&this.checkReservedType(e.local.name,e.local.loc.start,!0),!o||s||h||this.checkReservedWord(e.local.name,e.loc.start,!0,!0),this.finishImportSpecifier(e,"ImportSpecifier")}parseBindingAtom(){return 78===this.state.type?this.parseIdentifier(!0):super.parseBindingAtom()}parseFunctionParams(e,t){let s=e.kind;"get"!==s&&"set"!==s&&this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),super.parseFunctionParams(e,t)}parseVarId(e,t){super.parseVarId(e,t),this.match(14)&&(e.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(e.id))}parseAsyncArrowFromCallExpression(e,t){if(this.match(14)){let t=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,e.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=t}return super.parseAsyncArrowFromCallExpression(e,t)}shouldParseAsyncArrow(){return this.match(14)||super.shouldParseAsyncArrow()}parseMaybeAssign(e,t){var s,i,r;let a,n=null;if(this.hasPlugin("jsx")&&(this.match(143)||this.match(47))){if(n=this.state.clone(),!(a=this.tryParse(()=>super.parseMaybeAssign(e,t),n)).error)return a.node;let{context:s}=this.state,i=s[s.length-1];(i===P.j_oTag||i===P.j_expr)&&s.pop()}if(null!=(s=a)&&s.error||this.match(47)){let s;n=n||this.state.clone();let o=this.tryParse(i=>{var r;s=this.flowParseTypeParameterDeclaration();let a=this.forwardNoArrowParamsConversionAt(s,()=>{let i=super.parseMaybeAssign(e,t);return this.resetStartLocationFromNode(i,s),i});null!=(r=a.extra)&&r.parenthesized&&i();let n=this.maybeUnwrapTypeCastExpression(a);return"ArrowFunctionExpression"!==n.type&&i(),n.typeParameters=s,this.resetStartLocationFromNode(n,s),a},n),h=null;if(o.node&&"ArrowFunctionExpression"===this.maybeUnwrapTypeCastExpression(o.node).type){if(!o.error&&!o.aborted)return o.node.async&&this.raise(eH.UnexpectedTypeParameterBeforeAsyncArrowFunction,s),o.node;h=o.node}if(null!=(i=a)&&i.node)return this.state=a.failState,a.node;if(h)return this.state=o.failState,h;if(null!=(r=a)&&r.thrown)throw a.error;if(o.thrown)throw o.error;throw this.raise(eH.UnexpectedTokenAfterTypeParameter,s)}return super.parseMaybeAssign(e,t)}parseArrow(e){if(this.match(14)){let t=this.tryParse(()=>{let t=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0;let s=this.startNode();return[s.typeAnnotation,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),this.state.noAnonFunctionType=t,this.canInsertSemicolon()&&this.unexpected(),this.match(19)||this.unexpected(),s});if(t.thrown)return null;t.error&&(this.state=t.failState),e.returnType=t.node.typeAnnotation?this.finishNode(t.node,"TypeAnnotation"):null}return super.parseArrow(e)}shouldParseArrow(e){return this.match(14)||super.shouldParseArrow(e)}setArrowFunctionParameters(e,t){this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(e.start))?e.params=t:super.setArrowFunctionParameters(e,t)}checkParams(e,t,s,i=!0){if(!(s&&this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(e.start)))){for(let t=0;t<e.params.length;t++)this.isThisParam(e.params[t])&&t>0&&this.raise(eH.ThisParamMustBeFirst,e.params[t]);super.checkParams(e,t,s,i)}}parseParenAndDistinguishExpression(e){return super.parseParenAndDistinguishExpression(e&&!this.state.noArrowAt.includes(this.sourceToOffsetPos(this.state.start)))}parseSubscripts(e,t,s){if("Identifier"===e.type&&"async"===e.name&&this.state.noArrowAt.includes(t.index)){this.next();let s=this.startNodeAt(t);s.callee=e,s.arguments=super.parseCallExpressionArguments(11),e=this.finishNode(s,"CallExpression")}else if("Identifier"===e.type&&"async"===e.name&&this.match(47)){let i=this.state.clone(),r=this.tryParse(e=>this.parseAsyncArrowWithTypeParameters(t)||e(),i);if(!r.error&&!r.aborted)return r.node;let a=this.tryParse(()=>super.parseSubscripts(e,t,s),i);if(a.node&&!a.error)return a.node;if(r.node)return this.state=r.failState,r.node;if(a.node)return this.state=a.failState,a.node;throw r.error||a.error}return super.parseSubscripts(e,t,s)}parseSubscript(e,t,s,i){if(this.match(18)&&this.isLookaheadToken_lt()){if(i.optionalChainMember=!0,s)return i.stop=!0,e;this.next();let r=this.startNodeAt(t);return r.callee=e,r.typeArguments=this.flowParseTypeParameterInstantiationInExpression(),this.expect(10),r.arguments=this.parseCallExpressionArguments(11),r.optional=!0,this.finishCallExpression(r,!0)}if(!s&&this.shouldParseTypes()&&(this.match(47)||this.match(51))){let s=this.startNodeAt(t);s.callee=e;let r=this.tryParse(()=>(s.typeArguments=this.flowParseTypeParameterInstantiationCallOrNew(),this.expect(10),s.arguments=super.parseCallExpressionArguments(11),i.optionalChainMember&&(s.optional=!1),this.finishCallExpression(s,i.optionalChainMember)));if(r.node)return r.error&&(this.state=r.failState),r.node}return super.parseSubscript(e,t,s,i)}parseNewCallee(e){super.parseNewCallee(e);let t=null;this.shouldParseTypes()&&this.match(47)&&(t=this.tryParse(()=>this.flowParseTypeParameterInstantiationCallOrNew()).node),e.typeArguments=t}parseAsyncArrowWithTypeParameters(e){let t=this.startNodeAt(e);if(this.parseFunctionParams(t,!1),this.parseArrow(t))return super.parseArrowExpression(t,void 0,!0)}readToken_mult_modulo(e){let t=this.input.charCodeAt(this.state.pos+1);if(42===e&&47===t&&this.state.hasFlowComment){this.state.hasFlowComment=!1,this.state.pos+=2,this.nextToken();return}super.readToken_mult_modulo(e)}readToken_pipe_amp(e){let t=this.input.charCodeAt(this.state.pos+1);if(124===e&&125===t)return void this.finishOp(9,2);super.readToken_pipe_amp(e)}parseTopLevel(e,t){let s=super.parseTopLevel(e,t);return this.state.hasFlowComment&&this.raise(eH.UnterminatedFlowComment,this.state.curPosition()),s}skipBlockComment(){if(this.hasPlugin("flowComments")&&this.skipFlowComment()){if(this.state.hasFlowComment)throw this.raise(eH.NestedFlowComment,this.state.startLoc);this.hasFlowCommentCompletion();let e=this.skipFlowComment();e&&(this.state.pos+=e,this.state.hasFlowComment=!0);return}return super.skipBlockComment(this.state.hasFlowComment?"*-/":"*/")}skipFlowComment(){let{pos:e}=this.state,t=2;for(;[32,9].includes(this.input.charCodeAt(e+t));)t++;let s=this.input.charCodeAt(t+e),i=this.input.charCodeAt(t+e+1);return 58===s&&58===i?t+2:"flow-include"===this.input.slice(t+e,t+e+12)?t+12:58===s&&58!==i&&t}hasFlowCommentCompletion(){if(-1===this.input.indexOf("*/",this.state.pos))throw this.raise(d.UnterminatedComment,this.state.curPosition())}flowEnumErrorBooleanMemberNotInitialized(e,{enumName:t,memberName:s}){this.raise(eH.EnumBooleanMemberNotInitialized,e,{memberName:s,enumName:t})}flowEnumErrorInvalidMemberInitializer(e,t){return this.raise(t.explicitType?"symbol"===t.explicitType?eH.EnumInvalidMemberInitializerSymbolType:eH.EnumInvalidMemberInitializerPrimaryType:eH.EnumInvalidMemberInitializerUnknownType,e,t)}flowEnumErrorNumberMemberNotInitialized(e,t){this.raise(eH.EnumNumberMemberNotInitialized,e,t)}flowEnumErrorStringMemberInconsistentlyInitialized(e,t){this.raise(eH.EnumStringMemberInconsistentlyInitialized,e,t)}flowEnumMemberInit(){let e=this.state.startLoc,t=()=>this.match(12)||this.match(8);switch(this.state.type){case 135:{let s=this.parseNumericLiteral(this.state.value);if(t())return{type:"number",loc:s.loc.start,value:s};return{type:"invalid",loc:e}}case 134:{let s=this.parseStringLiteral(this.state.value);if(t())return{type:"string",loc:s.loc.start,value:s};return{type:"invalid",loc:e}}case 85:case 86:{let s=this.parseBooleanLiteral(this.match(85));if(t())return{type:"boolean",loc:s.loc.start,value:s};return{type:"invalid",loc:e}}default:return{type:"invalid",loc:e}}}flowEnumMemberRaw(){let e=this.state.startLoc;return{id:this.parseIdentifier(!0),init:this.eat(29)?this.flowEnumMemberInit():{type:"none",loc:e}}}flowEnumCheckExplicitTypeMismatch(e,t,s){let{explicitType:i}=t;null!==i&&i!==s&&this.flowEnumErrorInvalidMemberInitializer(e,t)}flowEnumMembers({enumName:e,explicitType:t}){let s=new Set,i={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]},r=!1;for(;!this.match(8);){if(this.eat(21)){r=!0;break}let a=this.startNode(),{id:n,init:o}=this.flowEnumMemberRaw(),h=n.name;if(""===h)continue;/^[a-z]/.test(h)&&this.raise(eH.EnumInvalidMemberName,n,{memberName:h,suggestion:h[0].toUpperCase()+h.slice(1),enumName:e}),s.has(h)&&this.raise(eH.EnumDuplicateMemberName,n,{memberName:h,enumName:e}),s.add(h);let l={enumName:e,explicitType:t,memberName:h};switch(a.id=n,o.type){case"boolean":this.flowEnumCheckExplicitTypeMismatch(o.loc,l,"boolean"),a.init=o.value,i.booleanMembers.push(this.finishNode(a,"EnumBooleanMember"));break;case"number":this.flowEnumCheckExplicitTypeMismatch(o.loc,l,"number"),a.init=o.value,i.numberMembers.push(this.finishNode(a,"EnumNumberMember"));break;case"string":this.flowEnumCheckExplicitTypeMismatch(o.loc,l,"string"),a.init=o.value,i.stringMembers.push(this.finishNode(a,"EnumStringMember"));break;case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(o.loc,l);case"none":switch(t){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(o.loc,l);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(o.loc,l);break;default:i.defaultedMembers.push(this.finishNode(a,"EnumDefaultedMember"))}}this.match(8)||this.expect(12)}return{members:i,hasUnknownMembers:r}}flowEnumStringMembers(e,t,{enumName:s}){if(0===e.length)return t;if(0===t.length)return e;if(t.length>e.length){for(let t of e)this.flowEnumErrorStringMemberInconsistentlyInitialized(t,{enumName:s});return t}for(let e of t)this.flowEnumErrorStringMemberInconsistentlyInitialized(e,{enumName:s});return e}flowEnumParseExplicitType({enumName:e}){if(!this.eatContextual(102))return null;if(!D(this.state.type))throw this.raise(eH.EnumInvalidExplicitTypeUnknownSupplied,this.state.startLoc,{enumName:e});let{value:t}=this.state;return this.next(),"boolean"!==t&&"number"!==t&&"string"!==t&&"symbol"!==t&&this.raise(eH.EnumInvalidExplicitType,this.state.startLoc,{enumName:e,invalidEnumType:t}),t}flowEnumBody(e,t){let s=t.name,i=t.loc.start,r=this.flowEnumParseExplicitType({enumName:s});this.expect(5);let{members:a,hasUnknownMembers:n}=this.flowEnumMembers({enumName:s,explicitType:r});switch(e.hasUnknownMembers=n,r){case"boolean":return e.explicitType=!0,e.members=a.booleanMembers,this.expect(8),this.finishNode(e,"EnumBooleanBody");case"number":return e.explicitType=!0,e.members=a.numberMembers,this.expect(8),this.finishNode(e,"EnumNumberBody");case"string":return e.explicitType=!0,e.members=this.flowEnumStringMembers(a.stringMembers,a.defaultedMembers,{enumName:s}),this.expect(8),this.finishNode(e,"EnumStringBody");case"symbol":return e.members=a.defaultedMembers,this.expect(8),this.finishNode(e,"EnumSymbolBody");default:{let t=()=>(e.members=[],this.expect(8),this.finishNode(e,"EnumStringBody"));e.explicitType=!1;let r=a.booleanMembers.length,n=a.numberMembers.length,o=a.stringMembers.length,h=a.defaultedMembers.length;if(!r&&!n&&!o&&!h)return t();if(!r&&!n)return e.members=this.flowEnumStringMembers(a.stringMembers,a.defaultedMembers,{enumName:s}),this.expect(8),this.finishNode(e,"EnumStringBody");if(n||o||!(r>=h))if(r||o||!(n>=h))return this.raise(eH.EnumInconsistentMemberValues,i,{enumName:s}),t();else{for(let e of a.defaultedMembers)this.flowEnumErrorNumberMemberNotInitialized(e.loc.start,{enumName:s,memberName:e.id.name});return e.members=a.numberMembers,this.expect(8),this.finishNode(e,"EnumNumberBody")}else{for(let e of a.defaultedMembers)this.flowEnumErrorBooleanMemberNotInitialized(e.loc.start,{enumName:s,memberName:e.id.name});return e.members=a.booleanMembers,this.expect(8),this.finishNode(e,"EnumBooleanBody")}}}}flowParseEnumDeclaration(e){let t=this.parseIdentifier();return e.id=t,e.body=this.flowEnumBody(this.startNode(),t),this.finishNode(e,"EnumDeclaration")}jsxParseOpeningElementAfterName(e){return this.shouldParseTypes()&&(this.match(47)||this.match(51))&&(e.typeArguments=this.flowParseTypeParameterInstantiationInExpression()),super.jsxParseOpeningElementAfterName(e)}isLookaheadToken_lt(){let e=this.nextTokenStart();if(60===this.input.charCodeAt(e)){let t=this.input.charCodeAt(e+1);return 60!==t&&61!==t}return!1}reScan_lt_gt(){let{type:e}=this.state;47===e?(this.state.pos-=1,this.readToken_lt()):48===e&&(this.state.pos-=1,this.readToken_gt())}reScan_lt(){let{type:e}=this.state;return 51===e?(this.state.pos-=2,this.finishOp(47,1),47):e}maybeUnwrapTypeCastExpression(e){return"TypeCastExpression"===e.type?e.expression:e}},typescript:e=>class extends e{constructor(...e){super(...e),this.tsParseInOutModifiers=this.tsParseModifiers.bind(this,{allowedModifiers:["in","out"],disallowedModifiers:["const","public","private","protected","readonly","declare","abstract","override"],errorTemplate:e1.InvalidModifierOnTypeParameter}),this.tsParseConstModifier=this.tsParseModifiers.bind(this,{allowedModifiers:["const"],disallowedModifiers:["in","out"],errorTemplate:e1.InvalidModifierOnTypeParameterPositions}),this.tsParseInOutConstModifiers=this.tsParseModifiers.bind(this,{allowedModifiers:["in","out","const"],disallowedModifiers:["public","private","protected","readonly","declare","abstract","override"],errorTemplate:e1.InvalidModifierOnTypeParameter})}getScopeHandler(){return eG}tsIsIdentifier(){return D(this.state.type)}tsTokenCanFollowModifier(){return this.match(0)||this.match(5)||this.match(55)||this.match(21)||this.match(139)||this.isLiteralPropertyName()}tsNextTokenOnSameLineAndCanFollowModifier(){return this.next(),!this.hasPrecedingLineBreak()&&this.tsTokenCanFollowModifier()}tsNextTokenCanFollowModifier(){return this.match(106)?(this.next(),this.tsTokenCanFollowModifier()):this.tsNextTokenOnSameLineAndCanFollowModifier()}tsParseModifier(e,t){if(!D(this.state.type)&&58!==this.state.type&&75!==this.state.type)return;let s=this.state.value;if(e.includes(s)){if(t&&this.tsIsStartOfStaticBlocks())return;if(this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this)))return s}}tsParseModifiers({allowedModifiers:e,disallowedModifiers:t,stopOnStartOfClassStaticBlock:s,errorTemplate:i=e1.InvalidModifierOnTypeMember},r){let a=(e,t,s,i)=>{t===s&&r[i]&&this.raise(e1.InvalidModifiersOrder,e,{orderedModifiers:[s,i]})},n=(e,t,s,i)=>{(r[s]&&t===i||r[i]&&t===s)&&this.raise(e1.IncompatibleModifiers,e,{modifiers:[s,i]})};for(;;){let{startLoc:o}=this.state,h=this.tsParseModifier(e.concat(null!=t?t:[]),s);if(!h)break;if(e0(h))r.accessibility?this.raise(e1.DuplicateAccessibilityModifier,o,{modifier:h}):(a(o,h,h,"override"),a(o,h,h,"static"),a(o,h,h,"readonly"),r.accessibility=h);else"in"===h||"out"===h?(r[h]&&this.raise(e1.DuplicateModifier,o,{modifier:h}),r[h]=!0,a(o,h,"in","out")):(hasOwnProperty.call(r,h)?this.raise(e1.DuplicateModifier,o,{modifier:h}):(a(o,h,"static","readonly"),a(o,h,"static","override"),a(o,h,"override","readonly"),a(o,h,"abstract","override"),n(o,h,"declare","override"),n(o,h,"static","abstract")),r[h]=!0);null!=t&&t.includes(h)&&this.raise(i,o,{modifier:h})}}tsIsListTerminator(e){switch(e){case"EnumMembers":case"TypeMembers":return this.match(8);case"HeritageClauseElement":return this.match(5);case"TupleElementTypes":return this.match(3);case"TypeParametersOrArguments":return this.match(48)}}tsParseList(e,t){let s=[];for(;!this.tsIsListTerminator(e);)s.push(t());return s}tsParseDelimitedList(e,t,s){var i=this.tsParseDelimitedListWorker(e,t,!0,s);if(null==i)throw Error(`Unexpected ${i} value.`);return i}tsParseDelimitedListWorker(e,t,s,i){let r=[],a=-1;for(;!this.tsIsListTerminator(e);){a=-1;let i=t();if(null==i)return;if(r.push(i),this.eat(12)){a=this.state.lastTokStartLoc.index;continue}if(this.tsIsListTerminator(e))break;s&&this.expect(12);return}return i&&(i.value=a),r}tsParseBracketedList(e,t,s,i,r){i||(s?this.expect(0):this.expect(47));let a=this.tsParseDelimitedList(e,t,r);return s?this.expect(3):this.expect(48),a}tsParseImportType(){let e=this.startNode();return this.expect(83),this.expect(10),this.match(134)?e.argument=this.parseStringLiteral(this.state.value):(this.raise(e1.UnsupportedImportTypeArgument,this.state.startLoc),e.argument=super.parseExprAtom()),this.eat(12)&&!this.match(11)?(e.options=super.parseMaybeAssignAllowIn(),this.eat(12)):e.options=null,this.expect(11),this.eat(16)&&(e.qualifier=this.tsParseEntityName(3)),this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSImportType")}tsParseEntityName(e){let t;if(1&e&&this.match(78))if(2&e)t=this.parseIdentifier(!0);else{let e=this.startNode();this.next(),t=this.finishNode(e,"ThisExpression")}else t=this.parseIdentifier(!!(1&e));for(;this.eat(16);){let s=this.startNodeAtNode(t);s.left=t,s.right=this.parseIdentifier(!!(1&e)),t=this.finishNode(s,"TSQualifiedName")}return t}tsParseTypeReference(){let e=this.startNode();return e.typeName=this.tsParseEntityName(1),!this.hasPrecedingLineBreak()&&this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSTypeReference")}tsParseThisTypePredicate(e){this.next();let t=this.startNodeAtNode(e);return t.parameterName=e,t.typeAnnotation=this.tsParseTypeAnnotation(!1),t.asserts=!1,this.finishNode(t,"TSTypePredicate")}tsParseThisTypeNode(){let e=this.startNode();return this.next(),this.finishNode(e,"TSThisType")}tsParseTypeQuery(){let e=this.startNode();return this.expect(87),this.match(83)?e.exprName=this.tsParseImportType():e.exprName=this.tsParseEntityName(3),!this.hasPrecedingLineBreak()&&this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSTypeQuery")}tsParseTypeParameter(e){let t=this.startNode();return e(t),t.name=this.tsParseTypeParameterName(),t.constraint=this.tsEatThenParseType(81),t.default=this.tsEatThenParseType(29),this.finishNode(t,"TSTypeParameter")}tsTryParseTypeParameters(e){if(this.match(47))return this.tsParseTypeParameters(e)}tsParseTypeParameters(e){let t=this.startNode();this.match(47)||this.match(143)?this.next():this.unexpected();let s={value:-1};return t.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this,e),!1,!0,s),0===t.params.length&&this.raise(e1.EmptyTypeParameters,t),-1!==s.value&&this.addExtra(t,"trailingComma",s.value),this.finishNode(t,"TSTypeParameterDeclaration")}tsFillSignature(e,t){let s=19===e,i="typeAnnotation";t.typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier),this.expect(10),t.parameters=this.tsParseBindingListForSignature(),s?t[i]=this.tsParseTypeOrTypePredicateAnnotation(e):this.match(e)&&(t[i]=this.tsParseTypeOrTypePredicateAnnotation(e))}tsParseBindingListForSignature(){let e=super.parseBindingList(11,41,2);for(let t of e){let{type:e}=t;("AssignmentPattern"===e||"TSParameterProperty"===e)&&this.raise(e1.UnsupportedSignatureParameterKind,t,{type:e})}return e}tsParseTypeMemberSemicolon(){this.eat(12)||this.isLineTerminator()||this.expect(13)}tsParseSignatureMember(e,t){return this.tsFillSignature(14,t),this.tsParseTypeMemberSemicolon(),this.finishNode(t,e)}tsIsUnambiguouslyIndexSignature(){return this.next(),!!D(this.state.type)&&(this.next(),this.match(14))}tsTryParseIndexSignature(e){if(!(this.match(0)&&this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this))))return;this.expect(0);let t=this.parseIdentifier();t.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(t),this.expect(3),e.parameters=[t];let s=this.tsTryParseTypeAnnotation();return s&&(e.typeAnnotation=s),this.tsParseTypeMemberSemicolon(),this.finishNode(e,"TSIndexSignature")}tsParsePropertyOrMethodSignature(e,t){if(this.eat(17)&&(e.optional=!0),this.match(10)||this.match(47)){t&&this.raise(e1.ReadonlyForMethodSignature,e),e.kind&&this.match(47)&&this.raise(e1.AccessorCannotHaveTypeParameters,this.state.curPosition()),this.tsFillSignature(14,e),this.tsParseTypeMemberSemicolon();let s="parameters",i="typeAnnotation";if("get"===e.kind)e[s].length>0&&(this.raise(d.BadGetterArity,this.state.curPosition()),this.isThisParam(e[s][0])&&this.raise(e1.AccessorCannotDeclareThisParameter,this.state.curPosition()));else if("set"===e.kind){if(1!==e[s].length)this.raise(d.BadSetterArity,this.state.curPosition());else{let t=e[s][0];this.isThisParam(t)&&this.raise(e1.AccessorCannotDeclareThisParameter,this.state.curPosition()),"Identifier"===t.type&&t.optional&&this.raise(e1.SetAccessorCannotHaveOptionalParameter,this.state.curPosition()),"RestElement"===t.type&&this.raise(e1.SetAccessorCannotHaveRestParameter,this.state.curPosition())}e[i]&&this.raise(e1.SetAccessorCannotHaveReturnType,e[i])}else e.kind="method";return this.finishNode(e,"TSMethodSignature")}{t&&(e.readonly=!0);let s=this.tsTryParseTypeAnnotation();return s&&(e.typeAnnotation=s),this.tsParseTypeMemberSemicolon(),this.finishNode(e,"TSPropertySignature")}}tsParseTypeMember(){let e=this.startNode();if(this.match(10)||this.match(47))return this.tsParseSignatureMember("TSCallSignatureDeclaration",e);if(this.match(77)){let t=this.startNode();return(this.next(),this.match(10)||this.match(47))?this.tsParseSignatureMember("TSConstructSignatureDeclaration",e):(e.key=this.createIdentifier(t,"new"),this.tsParsePropertyOrMethodSignature(e,!1))}this.tsParseModifiers({allowedModifiers:["readonly"],disallowedModifiers:["declare","abstract","private","protected","public","static","override"]},e);let t=this.tsTryParseIndexSignature(e);return t||(super.parsePropertyName(e),!e.computed&&"Identifier"===e.key.type&&("get"===e.key.name||"set"===e.key.name)&&this.tsTokenCanFollowModifier()&&(e.kind=e.key.name,super.parsePropertyName(e)),this.tsParsePropertyOrMethodSignature(e,!!e.readonly))}tsParseTypeLiteral(){let e=this.startNode();return e.members=this.tsParseObjectTypeMembers(),this.finishNode(e,"TSTypeLiteral")}tsParseObjectTypeMembers(){this.expect(5);let e=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(8),e}tsIsStartOfMappedType(){return(this.next(),this.eat(53))?this.isContextual(122):(this.isContextual(122)&&this.next(),!!this.match(0)&&(this.next(),!!this.tsIsIdentifier()&&(this.next(),this.match(58))))}tsParseMappedType(){let e=this.startNode();this.expect(5),this.match(53)?(e.readonly=this.state.value,this.next(),this.expectContextual(122)):this.eatContextual(122)&&(e.readonly=!0),this.expect(0);{let t=this.startNode();t.name=this.tsParseTypeParameterName(),t.constraint=this.tsExpectThenParseType(58),e.typeParameter=this.finishNode(t,"TSTypeParameter")}return e.nameType=this.eatContextual(93)?this.tsParseType():null,this.expect(3),this.match(53)?(e.optional=this.state.value,this.next(),this.expect(17)):this.eat(17)&&(e.optional=!0),e.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(8),this.finishNode(e,"TSMappedType")}tsParseTupleType(){let e=this.startNode();e.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);let t=!1;return e.elementTypes.forEach(e=>{let{type:s}=e;t&&"TSRestType"!==s&&"TSOptionalType"!==s&&!("TSNamedTupleMember"===s&&e.optional)&&this.raise(e1.OptionalTypeBeforeRequired,e),t||(t="TSNamedTupleMember"===s&&e.optional||"TSOptionalType"===s)}),this.finishNode(e,"TSTupleType")}tsParseTupleElementType(){let e,t,s,i,r=this.state.startLoc,a=this.eat(21),{startLoc:n}=this.state,o=O(this.state.type)?this.lookaheadCharCode():null;if(58===o)e=!0,s=!1,t=this.parseIdentifier(!0),this.expect(14),i=this.tsParseType();else if(63===o){s=!0;let r=this.state.value,a=this.tsParseNonArrayType();58===this.lookaheadCharCode()?(e=!0,t=this.createIdentifier(this.startNodeAt(n),r),this.expect(17),this.expect(14),i=this.tsParseType()):(e=!1,i=a,this.expect(17))}else i=this.tsParseType(),s=this.eat(17),e=this.eat(14);if(e){let e;t?((e=this.startNodeAt(n)).optional=s,e.label=t,e.elementType=i,this.eat(17)&&(e.optional=!0,this.raise(e1.TupleOptionalAfterType,this.state.lastTokStartLoc))):((e=this.startNodeAt(n)).optional=s,this.raise(e1.InvalidTupleMemberLabel,i),e.label=i,e.elementType=this.tsParseType()),i=this.finishNode(e,"TSNamedTupleMember")}else if(s){let e=this.startNodeAt(n);e.typeAnnotation=i,i=this.finishNode(e,"TSOptionalType")}if(a){let e=this.startNodeAt(r);e.typeAnnotation=i,i=this.finishNode(e,"TSRestType")}return i}tsParseParenthesizedType(){let e=this.startNode();return this.expect(10),e.typeAnnotation=this.tsParseType(),this.expect(11),this.finishNode(e,"TSParenthesizedType")}tsParseFunctionOrConstructorType(e,t){let s=this.startNode();return"TSConstructorType"===e&&(s.abstract=!!t,t&&this.next(),this.next()),this.tsInAllowConditionalTypesContext(()=>this.tsFillSignature(19,s)),this.finishNode(s,e)}tsParseLiteralTypeNode(){let e=this.startNode();switch(this.state.type){case 135:case 136:case 134:case 85:case 86:e.literal=super.parseExprAtom();break;default:this.unexpected()}return this.finishNode(e,"TSLiteralType")}tsParseTemplateLiteralType(){{let e=this.startNode();return e.literal=super.parseTemplate(!1),this.finishNode(e,"TSLiteralType")}}parseTemplateSubstitution(){return this.state.inType?this.tsParseType():super.parseTemplateSubstitution()}tsParseThisTypeOrThisTypePredicate(){let e=this.tsParseThisTypeNode();return this.isContextual(116)&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(e):e}tsParseNonArrayType(){switch(this.state.type){case 134:case 135:case 136:case 85:case 86:return this.tsParseLiteralTypeNode();case 53:if("-"===this.state.value){let e=this.startNode(),t=this.lookahead();return 135!==t.type&&136!==t.type&&this.unexpected(),e.literal=this.parseMaybeUnary(),this.finishNode(e,"TSLiteralType")}break;case 78:return this.tsParseThisTypeOrThisTypePredicate();case 87:return this.tsParseTypeQuery();case 83:return this.tsParseImportType();case 5:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case 0:return this.tsParseTupleType();case 10:return this.tsParseParenthesizedType();case 25:case 24:return this.tsParseTemplateLiteralType();default:{let{type:e}=this.state;if(D(e)||88===e||84===e){let t=88===e?"TSVoidKeyword":84===e?"TSNullKeyword":function(e){switch(e){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}(this.state.value);if(void 0!==t&&46!==this.lookaheadCharCode()){let e=this.startNode();return this.next(),this.finishNode(e,t)}return this.tsParseTypeReference()}}}this.unexpected()}tsParseArrayTypeOrHigher(){let{startLoc:e}=this.state,t=this.tsParseNonArrayType();for(;!this.hasPrecedingLineBreak()&&this.eat(0);)if(this.match(3)){let s=this.startNodeAt(e);s.elementType=t,this.expect(3),t=this.finishNode(s,"TSArrayType")}else{let s=this.startNodeAt(e);s.objectType=t,s.indexType=this.tsParseType(),this.expect(3),t=this.finishNode(s,"TSIndexedAccessType")}return t}tsParseTypeOperator(){let e=this.startNode(),t=this.state.value;return this.next(),e.operator=t,e.typeAnnotation=this.tsParseTypeOperatorOrHigher(),"readonly"===t&&this.tsCheckTypeAnnotationForReadOnly(e),this.finishNode(e,"TSTypeOperator")}tsCheckTypeAnnotationForReadOnly(e){switch(e.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(e1.UnexpectedReadonly,e)}}tsParseInferType(){let e=this.startNode();this.expectContextual(115);let t=this.startNode();return t.name=this.tsParseTypeParameterName(),t.constraint=this.tsTryParse(()=>this.tsParseConstraintForInferType()),e.typeParameter=this.finishNode(t,"TSTypeParameter"),this.finishNode(e,"TSInferType")}tsParseConstraintForInferType(){if(this.eat(81)){let e=this.tsInDisallowConditionalTypesContext(()=>this.tsParseType());if(this.state.inDisallowConditionalTypesContext||!this.match(17))return e}}tsParseTypeOperatorOrHigher(){var e;return(e=this.state.type)>=121&&e<=123&&!this.state.containsEsc?this.tsParseTypeOperator():this.isContextual(115)?this.tsParseInferType():this.tsInAllowConditionalTypesContext(()=>this.tsParseArrayTypeOrHigher())}tsParseUnionOrIntersectionType(e,t,s){let i=this.startNode(),r=this.eat(s),a=[];do a.push(t());while(this.eat(s));return 1!==a.length||r?(i.types=a,this.finishNode(i,e)):a[0]}tsParseIntersectionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),45)}tsParseUnionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),43)}tsIsStartOfFunctionType(){return!!this.match(47)||this.match(10)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}tsSkipParameterStart(){if(D(this.state.type)||this.match(78))return this.next(),!0;if(this.match(5)){let{errors:e}=this.state,t=e.length;try{return this.parseObjectLike(8,!0),e.length===t}catch(e){return!1}}if(this.match(0)){this.next();let{errors:e}=this.state,t=e.length;try{return super.parseBindingList(3,93,1),e.length===t}catch(e){}}return!1}tsIsUnambiguouslyStartOfFunctionType(){if(this.next(),this.match(11)||this.match(21))return!0;if(this.tsSkipParameterStart()&&(this.match(14)||this.match(12)||this.match(17)||this.match(29)||this.match(11)&&(this.next(),this.match(19))))return!0;return!1}tsParseTypeOrTypePredicateAnnotation(e){return this.tsInType(()=>{let t=this.startNode();this.expect(e);let s=this.startNode(),i=!!this.tsTryParse(this.tsParseTypePredicateAsserts.bind(this));if(i&&this.match(78)){let e=this.tsParseThisTypeOrThisTypePredicate();return"TSThisType"===e.type?(s.parameterName=e,s.asserts=!0,s.typeAnnotation=null,e=this.finishNode(s,"TSTypePredicate")):(this.resetStartLocationFromNode(e,s),e.asserts=!0),t.typeAnnotation=e,this.finishNode(t,"TSTypeAnnotation")}let r=this.tsIsIdentifier()&&this.tsTryParse(this.tsParseTypePredicatePrefix.bind(this));if(!r)return i?(s.parameterName=this.parseIdentifier(),s.asserts=i,s.typeAnnotation=null,t.typeAnnotation=this.finishNode(s,"TSTypePredicate"),this.finishNode(t,"TSTypeAnnotation")):this.tsParseTypeAnnotation(!1,t);let a=this.tsParseTypeAnnotation(!1);return s.parameterName=r,s.typeAnnotation=a,s.asserts=i,t.typeAnnotation=this.finishNode(s,"TSTypePredicate"),this.finishNode(t,"TSTypeAnnotation")})}tsTryParseTypeOrTypePredicateAnnotation(){if(this.match(14))return this.tsParseTypeOrTypePredicateAnnotation(14)}tsTryParseTypeAnnotation(){if(this.match(14))return this.tsParseTypeAnnotation()}tsTryParseType(){return this.tsEatThenParseType(14)}tsParseTypePredicatePrefix(){let e=this.parseIdentifier();if(this.isContextual(116)&&!this.hasPrecedingLineBreak())return this.next(),e}tsParseTypePredicateAsserts(){if(109!==this.state.type)return!1;let e=this.state.containsEsc;return this.next(),(!!D(this.state.type)||!!this.match(78))&&(e&&this.raise(d.InvalidEscapedReservedWord,this.state.lastTokStartLoc,{reservedWord:"asserts"}),!0)}tsParseTypeAnnotation(e=!0,t=this.startNode()){return this.tsInType(()=>{e&&this.expect(14),t.typeAnnotation=this.tsParseType()}),this.finishNode(t,"TSTypeAnnotation")}tsParseType(){eZ(this.state.inType);let e=this.tsParseNonConditionalType();if(this.state.inDisallowConditionalTypesContext||this.hasPrecedingLineBreak()||!this.eat(81))return e;let t=this.startNodeAtNode(e);return t.checkType=e,t.extendsType=this.tsInDisallowConditionalTypesContext(()=>this.tsParseNonConditionalType()),this.expect(17),t.trueType=this.tsInAllowConditionalTypesContext(()=>this.tsParseType()),this.expect(14),t.falseType=this.tsInAllowConditionalTypesContext(()=>this.tsParseType()),this.finishNode(t,"TSConditionalType")}isAbstractConstructorSignature(){return this.isContextual(124)&&77===this.lookahead().type}tsParseNonConditionalType(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(77)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.isAbstractConstructorSignature()?this.tsParseFunctionOrConstructorType("TSConstructorType",!0):this.tsParseUnionTypeOrHigher()}tsParseTypeAssertion(){this.getPluginOption("typescript","disallowAmbiguousJSXLike")&&this.raise(e1.ReservedTypeAssertion,this.state.startLoc);let e=this.startNode();return e.typeAnnotation=this.tsInType(()=>(this.next(),this.match(75)?this.tsParseTypeReference():this.tsParseType())),this.expect(48),e.expression=this.parseMaybeUnary(),this.finishNode(e,"TSTypeAssertion")}tsParseHeritageClause(e){let t=this.state.startLoc,s=this.tsParseDelimitedList("HeritageClauseElement",()=>{{let e=this.startNode();return e.expression=this.tsParseEntityName(3),this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSExpressionWithTypeArguments")}});return s.length||this.raise(e1.EmptyHeritageClauseType,t,{token:e}),s}tsParseInterfaceDeclaration(e,t={}){if(this.hasFollowingLineBreak())return null;this.expectContextual(129),t.declare&&(e.declare=!0),D(this.state.type)?(e.id=this.parseIdentifier(),this.checkIdentifier(e.id,130)):(e.id=null,this.raise(e1.MissingInterfaceName,this.state.startLoc)),e.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers),this.eat(81)&&(e.extends=this.tsParseHeritageClause("extends"));let s=this.startNode();return s.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),e.body=this.finishNode(s,"TSInterfaceBody"),this.finishNode(e,"TSInterfaceDeclaration")}tsParseTypeAliasDeclaration(e){return e.id=this.parseIdentifier(),this.checkIdentifier(e.id,2),e.typeAnnotation=this.tsInType(()=>{if(e.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutModifiers),this.expect(29),this.isContextual(114)&&16!==this.lookahead().type){let e=this.startNode();return this.next(),this.finishNode(e,"TSIntrinsicKeyword")}return this.tsParseType()}),this.semicolon(),this.finishNode(e,"TSTypeAliasDeclaration")}tsInTopLevelContext(e){if(this.curContext()===P.brace)return e();{let t=this.state.context;this.state.context=[t[0]];try{return e()}finally{this.state.context=t}}}tsInType(e){let t=this.state.inType;this.state.inType=!0;try{return e()}finally{this.state.inType=t}}tsInDisallowConditionalTypesContext(e){let t=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!0;try{return e()}finally{this.state.inDisallowConditionalTypesContext=t}}tsInAllowConditionalTypesContext(e){let t=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!1;try{return e()}finally{this.state.inDisallowConditionalTypesContext=t}}tsEatThenParseType(e){if(this.match(e))return this.tsNextThenParseType()}tsExpectThenParseType(e){return this.tsInType(()=>(this.expect(e),this.tsParseType()))}tsNextThenParseType(){return this.tsInType(()=>(this.next(),this.tsParseType()))}tsParseEnumMember(){let e=this.startNode();return e.id=this.match(134)?super.parseStringLiteral(this.state.value):this.parseIdentifier(!0),this.eat(29)&&(e.initializer=super.parseMaybeAssignAllowIn()),this.finishNode(e,"TSEnumMember")}tsParseEnumDeclaration(e,t={}){return t.const&&(e.const=!0),t.declare&&(e.declare=!0),this.expectContextual(126),e.id=this.parseIdentifier(),this.checkIdentifier(e.id,e.const?8971:8459),this.expect(5),e.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(e,"TSEnumDeclaration")}tsParseEnumBody(){let e=this.startNode();return this.expect(5),e.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(e,"TSEnumBody")}tsParseModuleBlock(){let e=this.startNode();return this.scope.enter(0),this.expect(5),super.parseBlockOrModuleBlockBody(e.body=[],void 0,!0,8),this.scope.exit(),this.finishNode(e,"TSModuleBlock")}tsParseModuleOrNamespaceDeclaration(e,t=!1){if(e.id=this.parseIdentifier(),t||this.checkIdentifier(e.id,1024),this.eat(16)){let t=this.startNode();this.tsParseModuleOrNamespaceDeclaration(t,!0),e.body=t}else this.scope.enter(256),this.prodParam.enter(0),e.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit();return this.finishNode(e,"TSModuleDeclaration")}tsParseAmbientExternalModuleDeclaration(e){return this.isContextual(112)?(e.kind="global",e.global=!0,e.id=this.parseIdentifier()):this.match(134)?(e.kind="module",e.id=super.parseStringLiteral(this.state.value)):this.unexpected(),this.match(5)?(this.scope.enter(256),this.prodParam.enter(0),e.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit()):this.semicolon(),this.finishNode(e,"TSModuleDeclaration")}tsParseImportEqualsDeclaration(e,t,s){e.isExport=s||!1,e.id=t||this.parseIdentifier(),this.checkIdentifier(e.id,4096),this.expect(29);let i=this.tsParseModuleReference();return"type"===e.importKind&&"TSExternalModuleReference"!==i.type&&this.raise(e1.ImportAliasHasImportType,i),e.moduleReference=i,this.semicolon(),this.finishNode(e,"TSImportEqualsDeclaration")}tsIsExternalModuleReference(){return this.isContextual(119)&&40===this.lookaheadCharCode()}tsParseModuleReference(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(0)}tsParseExternalModuleReference(){let e=this.startNode();return this.expectContextual(119),this.expect(10),this.match(134)||this.unexpected(),e.expression=super.parseExprAtom(),this.expect(11),this.sawUnambiguousESM=!0,this.finishNode(e,"TSExternalModuleReference")}tsLookAhead(e){let t=this.state.clone(),s=e();return this.state=t,s}tsTryParseAndCatch(e){let t=this.tryParse(t=>e()||t());if(!t.aborted&&t.node)return t.error&&(this.state=t.failState),t.node}tsTryParse(e){let t=this.state.clone(),s=e();if(void 0!==s&&!1!==s)return s;this.state=t}tsTryParseDeclare(e){let t;if(this.isLineTerminator())return;let s=this.state.type;return this.isContextual(100)&&(s=74,t="let"),this.tsInAmbientContext(()=>{switch(s){case 68:return e.declare=!0,super.parseFunctionStatement(e,!1,!1);case 80:return e.declare=!0,this.parseClass(e,!0,!1);case 126:return this.tsParseEnumDeclaration(e,{declare:!0});case 112:return this.tsParseAmbientExternalModuleDeclaration(e);case 75:case 74:if(!this.match(75)||!this.isLookaheadContextual("enum"))return e.declare=!0,this.parseVarStatement(e,t||this.state.value,!0);return this.expect(75),this.tsParseEnumDeclaration(e,{const:!0,declare:!0});case 129:{let t=this.tsParseInterfaceDeclaration(e,{declare:!0});if(t)return t}default:if(D(s))return this.tsParseDeclaration(e,this.state.value,!0,null)}})}tsTryParseExportDeclaration(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0,null)}tsParseExpressionStatement(e,t,s){switch(t.name){case"declare":{let t=this.tsTryParseDeclare(e);return t&&(t.declare=!0),t}case"global":if(this.match(5))return this.scope.enter(256),this.prodParam.enter(0),e.kind="global",e.global=!0,e.id=t,e.body=this.tsParseModuleBlock(),this.scope.exit(),this.prodParam.exit(),this.finishNode(e,"TSModuleDeclaration");break;default:return this.tsParseDeclaration(e,t.name,!1,s)}}tsParseDeclaration(e,t,s,i){switch(t){case"abstract":if(this.tsCheckLineTerminator(s)&&(this.match(80)||D(this.state.type)))return this.tsParseAbstractDeclaration(e,i);break;case"module":if(this.tsCheckLineTerminator(s)){if(this.match(134))return this.tsParseAmbientExternalModuleDeclaration(e);else if(D(this.state.type))return e.kind="module",this.tsParseModuleOrNamespaceDeclaration(e)}break;case"namespace":if(this.tsCheckLineTerminator(s)&&D(this.state.type))return e.kind="namespace",this.tsParseModuleOrNamespaceDeclaration(e);break;case"type":if(this.tsCheckLineTerminator(s)&&D(this.state.type))return this.tsParseTypeAliasDeclaration(e)}}tsCheckLineTerminator(e){return e?!this.hasFollowingLineBreak()&&(this.next(),!0):!this.isLineTerminator()}tsTryParseGenericAsyncArrowFunction(e){if(!this.match(47))return;let t=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=!0;let s=this.tsTryParseAndCatch(()=>{let t=this.startNodeAt(e);return t.typeParameters=this.tsParseTypeParameters(this.tsParseConstModifier),super.parseFunctionParams(t),t.returnType=this.tsTryParseTypeOrTypePredicateAnnotation(),this.expect(19),t});if(this.state.maybeInArrowParameters=t,s)return super.parseArrowExpression(s,null,!0)}tsParseTypeArgumentsInExpression(){if(47===this.reScan_lt())return this.tsParseTypeArguments()}tsParseTypeArguments(){let e=this.startNode();return e.params=this.tsInType(()=>this.tsInTopLevelContext(()=>(this.expect(47),this.tsParseDelimitedList("TypeParametersOrArguments",this.tsParseType.bind(this))))),0===e.params.length?this.raise(e1.EmptyTypeArguments,e):this.state.inType||this.curContext()!==P.brace||this.reScan_lt_gt(),this.expect(48),this.finishNode(e,"TSTypeParameterInstantiation")}tsIsDeclarationStart(){var e;return(e=this.state.type)>=124&&e<=130}isExportDefaultSpecifier(){return!this.tsIsDeclarationStart()&&super.isExportDefaultSpecifier()}parseBindingElement(e,t){let s=this.state.startLoc,i={};this.tsParseModifiers({allowedModifiers:["public","private","protected","override","readonly"]},i);let r=i.accessibility,a=i.override,n=i.readonly;!(4&e)&&(r||n||a)&&this.raise(e1.UnexpectedParameterModifier,s);let o=this.parseMaybeDefault();2&e&&this.parseFunctionParamType(o);let h=this.parseMaybeDefault(o.loc.start,o);if(r||n||a){let e=this.startNodeAt(s);return t.length&&(e.decorators=t),r&&(e.accessibility=r),n&&(e.readonly=n),a&&(e.override=a),"Identifier"!==h.type&&"AssignmentPattern"!==h.type&&this.raise(e1.UnsupportedParameterPropertyKind,e),e.parameter=h,this.finishNode(e,"TSParameterProperty")}return t.length&&(o.decorators=t),h}isSimpleParameter(e){return"TSParameterProperty"===e.type&&super.isSimpleParameter(e.parameter)||super.isSimpleParameter(e)}tsDisallowOptionalPattern(e){for(let t of e.params)"Identifier"!==t.type&&t.optional&&!this.state.isAmbientContext&&this.raise(e1.PatternIsOptional,t)}setArrowFunctionParameters(e,t,s){super.setArrowFunctionParameters(e,t,s),this.tsDisallowOptionalPattern(e)}parseFunctionBodyAndFinish(e,t,s=!1){this.match(14)&&(e.returnType=this.tsParseTypeOrTypePredicateAnnotation(14));let i="FunctionDeclaration"===t?"TSDeclareFunction":"ClassMethod"===t||"ClassPrivateMethod"===t?"TSDeclareMethod":void 0;return i&&!this.match(5)&&this.isLineTerminator()?this.finishNode(e,i):"TSDeclareFunction"===i&&this.state.isAmbientContext&&(this.raise(e1.DeclareFunctionHasImplementation,e),e.declare)?super.parseFunctionBodyAndFinish(e,i,s):(this.tsDisallowOptionalPattern(e),super.parseFunctionBodyAndFinish(e,t,s))}registerFunctionStatementId(e){!e.body&&e.id?this.checkIdentifier(e.id,1024):super.registerFunctionStatementId(e)}tsCheckForInvalidTypeCasts(e){e.forEach(e=>{(null==e?void 0:e.type)==="TSTypeCastExpression"&&this.raise(e1.UnexpectedTypeAnnotation,e.typeAnnotation)})}toReferencedList(e,t){return this.tsCheckForInvalidTypeCasts(e),e}parseArrayLike(e,t,s,i){let r=super.parseArrayLike(e,t,s,i);return"ArrayExpression"===r.type&&this.tsCheckForInvalidTypeCasts(r.elements),r}parseSubscript(e,t,s,i){if(!this.hasPrecedingLineBreak()&&this.match(35)){this.state.canStartJSXElement=!1,this.next();let s=this.startNodeAt(t);return s.expression=e,this.finishNode(s,"TSNonNullExpression")}let r=!1;if(this.match(18)&&60===this.lookaheadCharCode()){if(s)return i.stop=!0,e;i.optionalChainMember=r=!0,this.next()}if(this.match(47)||this.match(51)){let a,n=this.tsTryParseAndCatch(()=>{if(!s&&this.atPossibleAsyncArrow(e)){let e=this.tsTryParseGenericAsyncArrowFunction(t);if(e)return e}let n=this.tsParseTypeArgumentsInExpression();if(!n)return;if(r&&!this.match(10)){a=this.state.curPosition();return}if(U(this.state.type)){let s=super.parseTaggedTemplateExpression(e,t,i);return s.typeParameters=n,s}if(!s&&this.eat(10)){let s=this.startNodeAt(t);return s.callee=e,s.arguments=this.parseCallExpressionArguments(11),this.tsCheckForInvalidTypeCasts(s.arguments),s.typeParameters=n,i.optionalChainMember&&(s.optional=r),this.finishCallExpression(s,i.optionalChainMember)}let o=this.state.type;if(48===o||52===o||10!==o&&N[o]&&!this.hasPrecedingLineBreak())return;let h=this.startNodeAt(t);return h.expression=e,h.typeParameters=n,this.finishNode(h,"TSInstantiationExpression")});if(a&&this.unexpected(a,10),n)return"TSInstantiationExpression"===n.type&&(this.match(16)||this.match(18)&&40!==this.lookaheadCharCode())&&this.raise(e1.InvalidPropertyAccessAfterInstantiationExpression,this.state.startLoc),n}return super.parseSubscript(e,t,s,i)}parseNewCallee(e){var t;super.parseNewCallee(e);let{callee:s}=e;"TSInstantiationExpression"!==s.type||null!=(t=s.extra)&&t.parenthesized||(e.typeParameters=s.typeParameters,e.callee=s.expression)}parseExprOp(e,t,s){let i;if(C[58]>s&&!this.hasPrecedingLineBreak()&&(this.isContextual(93)||(i=this.isContextual(120)))){let r=this.startNodeAt(t);return r.expression=e,r.typeAnnotation=this.tsInType(()=>(this.next(),this.match(75))?(i&&this.raise(d.UnexpectedKeyword,this.state.startLoc,{keyword:"const"}),this.tsParseTypeReference()):this.tsParseType()),this.finishNode(r,i?"TSSatisfiesExpression":"TSAsExpression"),this.reScan_lt_gt(),this.parseExprOp(r,t,s)}return super.parseExprOp(e,t,s)}checkReservedWord(e,t,s,i){this.state.isAmbientContext||super.checkReservedWord(e,t,s,i)}checkImportReflection(e){super.checkImportReflection(e),e.module&&"value"!==e.importKind&&this.raise(e1.ImportReflectionHasImportType,e.specifiers[0].loc.start)}checkDuplicateExports(){}isPotentialImportPhase(e){if(super.isPotentialImportPhase(e))return!0;if(this.isContextual(130)){let t=this.lookaheadCharCode();return e?123===t||42===t:61!==t}return!e&&this.isContextual(87)}applyImportPhase(e,t,s,i){super.applyImportPhase(e,t,s,i),t?e.exportKind="type"===s?"type":"value":e.importKind="type"===s||"typeof"===s?s:"value"}parseImport(e){let t;if(this.match(134))return e.importKind="value",super.parseImport(e);if(D(this.state.type)&&61===this.lookaheadCharCode())return e.importKind="value",this.tsParseImportEqualsDeclaration(e);if(this.isContextual(130)){let s=this.parseMaybeImportPhase(e,!1);if(61===this.lookaheadCharCode())return this.tsParseImportEqualsDeclaration(e,s);t=super.parseImportSpecifiersAndAfter(e,s)}else t=super.parseImport(e);return"type"===t.importKind&&t.specifiers.length>1&&"ImportDefaultSpecifier"===t.specifiers[0].type&&this.raise(e1.TypeImportCannotSpecifyDefaultAndNamed,t),t}parseExport(e,t){if(this.match(83)){this.next();let t=null;return this.isContextual(130)&&this.isPotentialImportPhase(!1)?t=this.parseMaybeImportPhase(e,!1):e.importKind="value",this.tsParseImportEqualsDeclaration(e,t,!0)}return this.eat(29)?(e.expression=super.parseExpression(),this.semicolon(),this.sawUnambiguousESM=!0,this.finishNode(e,"TSExportAssignment")):this.eatContextual(93)?(this.expectContextual(128),e.id=this.parseIdentifier(),this.semicolon(),this.finishNode(e,"TSNamespaceExportDeclaration")):super.parseExport(e,t)}isAbstractClass(){return this.isContextual(124)&&80===this.lookahead().type}parseExportDefaultExpression(){if(this.isAbstractClass()){let e=this.startNode();return this.next(),e.abstract=!0,this.parseClass(e,!0,!0)}if(this.match(129)){let e=this.tsParseInterfaceDeclaration(this.startNode());if(e)return e}return super.parseExportDefaultExpression()}parseVarStatement(e,t,s=!1){let{isAmbientContext:i}=this.state,r=super.parseVarStatement(e,t,s||i);if(!i)return r;for(let{id:e,init:s}of r.declarations)s&&("const"!==t||e.typeAnnotation?this.raise(e1.InitializerNotAllowedInAmbientContext,s):!function(e,t){var s;let{type:i}=e;if(null!=(s=e.extra)&&s.parenthesized)return!1;if(t){if("Literal"===i){let{value:t}=e;if("string"==typeof t||"boolean"==typeof t)return!0}}else if("StringLiteral"===i||"BooleanLiteral"===i)return!0;return!!(e2(e,t)||function(e,t){if("UnaryExpression"===e.type){let{operator:s,argument:i}=e;if("-"===s&&e2(i,t))return!0}return!1}(e,t)||"TemplateLiteral"===i&&0===e.expressions.length||function(e){if("MemberExpression"!==e.type)return!1;let{computed:t,property:s}=e;return(!t||"StringLiteral"===s.type||"TemplateLiteral"===s.type&&!(s.expressions.length>0))&&function e(t){return"Identifier"===t.type||"MemberExpression"===t.type&&!t.computed&&e(t.object)}(e.object)}(e))}(s,this.hasPlugin("estree"))&&this.raise(e1.ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference,s));return r}parseStatementContent(e,t){if(this.match(75)&&this.isLookaheadContextual("enum")){let e=this.startNode();return this.expect(75),this.tsParseEnumDeclaration(e,{const:!0})}if(this.isContextual(126))return this.tsParseEnumDeclaration(this.startNode());if(this.isContextual(129)){let e=this.tsParseInterfaceDeclaration(this.startNode());if(e)return e}return super.parseStatementContent(e,t)}parseAccessModifier(){return this.tsParseModifier(["public","protected","private"])}tsHasSomeModifiers(e,t){return t.some(t=>e0(t)?e.accessibility===t:!!e[t])}tsIsStartOfStaticBlocks(){return this.isContextual(106)&&123===this.lookaheadCharCode()}parseClassMember(e,t,s){let i=["declare","private","public","protected","override","abstract","readonly","static"];this.tsParseModifiers({allowedModifiers:i,disallowedModifiers:["in","out"],stopOnStartOfClassStaticBlock:!0,errorTemplate:e1.InvalidModifierOnTypeParameterPositions},t);let r=()=>{this.tsIsStartOfStaticBlocks()?(this.next(),this.next(),this.tsHasSomeModifiers(t,i)&&this.raise(e1.StaticBlockCannotHaveModifier,this.state.curPosition()),super.parseClassStaticBlock(e,t)):this.parseClassMemberWithIsStatic(e,t,s,!!t.static)};t.declare?this.tsInAmbientContext(r):r()}parseClassMemberWithIsStatic(e,t,s,i){let r=this.tsTryParseIndexSignature(t);if(r){e.body.push(r),t.abstract&&this.raise(e1.IndexSignatureHasAbstract,t),t.accessibility&&this.raise(e1.IndexSignatureHasAccessibility,t,{modifier:t.accessibility}),t.declare&&this.raise(e1.IndexSignatureHasDeclare,t),t.override&&this.raise(e1.IndexSignatureHasOverride,t);return}!this.state.inAbstractClass&&t.abstract&&this.raise(e1.NonAbstractClassHasAbstractMethod,t),t.override&&!s.hadSuperClass&&this.raise(e1.OverrideNotInSubClass,t),super.parseClassMemberWithIsStatic(e,t,s,i)}parsePostMemberNameModifiers(e){this.eat(17)&&(e.optional=!0),e.readonly&&this.match(10)&&this.raise(e1.ClassMethodHasReadonly,e),e.declare&&this.match(10)&&this.raise(e1.ClassMethodHasDeclare,e)}parseExpressionStatement(e,t,s){return("Identifier"===t.type?this.tsParseExpressionStatement(e,t,s):void 0)||super.parseExpressionStatement(e,t,s)}shouldParseExportDeclaration(){return!!this.tsIsDeclarationStart()||super.shouldParseExportDeclaration()}parseConditional(e,t,s){if(!this.match(17))return e;if(this.state.maybeInArrowParameters){let t=this.lookaheadCharCode();if(44===t||61===t||58===t||41===t)return this.setOptionalParametersError(s),e}return super.parseConditional(e,t,s)}parseParenItem(e,t){let s=super.parseParenItem(e,t);if(this.eat(17)&&(s.optional=!0,this.resetEndLocation(e)),this.match(14)){let s=this.startNodeAt(t);return s.expression=e,s.typeAnnotation=this.tsParseTypeAnnotation(),this.finishNode(s,"TSTypeCastExpression")}return e}parseExportDeclaration(e){if(!this.state.isAmbientContext&&this.isContextual(125))return this.tsInAmbientContext(()=>this.parseExportDeclaration(e));let t=this.state.startLoc,s=this.eatContextual(125);if(s&&(this.isContextual(125)||!this.shouldParseExportDeclaration()))throw this.raise(e1.ExpectedAmbientAfterExportDeclare,this.state.startLoc);let i=D(this.state.type)&&this.tsTryParseExportDeclaration()||super.parseExportDeclaration(e);return i?(("TSInterfaceDeclaration"===i.type||"TSTypeAliasDeclaration"===i.type||s)&&(e.exportKind="type"),s&&"TSImportEqualsDeclaration"!==i.type&&(this.resetStartLocation(i,t),i.declare=!0),i):null}parseClassId(e,t,s,i){if((!t||s)&&this.isContextual(113))return;super.parseClassId(e,t,s,e.declare?1024:8331);let r=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers);r&&(e.typeParameters=r)}parseClassPropertyAnnotation(e){!e.optional&&(this.eat(35)?e.definite=!0:this.eat(17)&&(e.optional=!0));let t=this.tsTryParseTypeAnnotation();t&&(e.typeAnnotation=t)}parseClassProperty(e){if(this.parseClassPropertyAnnotation(e),this.state.isAmbientContext&&!(e.readonly&&!e.typeAnnotation)&&this.match(29)&&this.raise(e1.DeclareClassFieldHasInitializer,this.state.startLoc),e.abstract&&this.match(29)){let{key:t}=e;this.raise(e1.AbstractPropertyHasInitializer,this.state.startLoc,{propertyName:"Identifier"!==t.type||e.computed?`[${this.input.slice(this.offsetToSourcePos(t.start),this.offsetToSourcePos(t.end))}]`:t.name})}return super.parseClassProperty(e)}parseClassPrivateProperty(e){return e.abstract&&this.raise(e1.PrivateElementHasAbstract,e),e.accessibility&&this.raise(e1.PrivateElementHasAccessibility,e,{modifier:e.accessibility}),this.parseClassPropertyAnnotation(e),super.parseClassPrivateProperty(e)}parseClassAccessorProperty(e){return this.parseClassPropertyAnnotation(e),e.optional&&this.raise(e1.AccessorCannotBeOptional,e),super.parseClassAccessorProperty(e)}pushClassMethod(e,t,s,i,r,a){let n=this.tsTryParseTypeParameters(this.tsParseConstModifier);n&&r&&this.raise(e1.ConstructorHasTypeParameters,n);let{declare:o=!1,kind:h}=t;o&&("get"===h||"set"===h)&&this.raise(e1.DeclareAccessor,t,{kind:h}),n&&(t.typeParameters=n),super.pushClassMethod(e,t,s,i,r,a)}pushClassPrivateMethod(e,t,s,i){let r=this.tsTryParseTypeParameters(this.tsParseConstModifier);r&&(t.typeParameters=r),super.pushClassPrivateMethod(e,t,s,i)}declareClassPrivateMethodInScope(e,t){"TSDeclareMethod"!==e.type&&("MethodDefinition"!==e.type||hasOwnProperty.call(e.value,"body"))&&super.declareClassPrivateMethodInScope(e,t)}parseClassSuper(e){super.parseClassSuper(e),e.superClass&&(this.match(47)||this.match(51))&&(e.superTypeParameters=this.tsParseTypeArgumentsInExpression()),this.eatContextual(113)&&(e.implements=this.tsParseHeritageClause("implements"))}parseObjPropValue(e,t,s,i,r,a,n){let o=this.tsTryParseTypeParameters(this.tsParseConstModifier);return o&&(e.typeParameters=o),super.parseObjPropValue(e,t,s,i,r,a,n)}parseFunctionParams(e,t){let s=this.tsTryParseTypeParameters(this.tsParseConstModifier);s&&(e.typeParameters=s),super.parseFunctionParams(e,t)}parseVarId(e,t){super.parseVarId(e,t),"Identifier"===e.id.type&&!this.hasPrecedingLineBreak()&&this.eat(35)&&(e.definite=!0);let s=this.tsTryParseTypeAnnotation();s&&(e.id.typeAnnotation=s,this.resetEndLocation(e.id))}parseAsyncArrowFromCallExpression(e,t){return this.match(14)&&(e.returnType=this.tsParseTypeAnnotation()),super.parseAsyncArrowFromCallExpression(e,t)}parseMaybeAssign(e,t){var s,i,r,a,n;let o,h,l,p;if(this.hasPlugin("jsx")&&(this.match(143)||this.match(47))){if(o=this.state.clone(),!(h=this.tryParse(()=>super.parseMaybeAssign(e,t),o)).error)return h.node;let{context:s}=this.state,i=s[s.length-1];(i===P.j_oTag||i===P.j_expr)&&s.pop()}if(!(null!=(s=h)&&s.error)&&!this.match(47))return super.parseMaybeAssign(e,t);o&&o!==this.state||(o=this.state.clone());let c=this.tryParse(s=>{var i,r;p=this.tsParseTypeParameters(this.tsParseConstModifier);let a=super.parseMaybeAssign(e,t);return("ArrowFunctionExpression"!==a.type||null!=(i=a.extra)&&i.parenthesized)&&s(),(null==(r=p)?void 0:r.params.length)!==0&&this.resetStartLocationFromNode(a,p),a.typeParameters=p,a},o);if(!c.error&&!c.aborted)return p&&this.reportReservedArrowTypeParam(p),c.node;if(!h&&(eZ(!this.hasPlugin("jsx")),!(l=this.tryParse(()=>super.parseMaybeAssign(e,t),o)).error))return l.node;if(null!=(i=h)&&i.node)return this.state=h.failState,h.node;if(c.node)return this.state=c.failState,p&&this.reportReservedArrowTypeParam(p),c.node;if(null!=(r=l)&&r.node)return this.state=l.failState,l.node;throw(null==(a=h)?void 0:a.error)||c.error||(null==(n=l)?void 0:n.error)}reportReservedArrowTypeParam(e){var t;1===e.params.length&&!e.params[0].constraint&&!(null!=(t=e.extra)&&t.trailingComma)&&this.getPluginOption("typescript","disallowAmbiguousJSXLike")&&this.raise(e1.ReservedArrowTypeParam,e)}parseMaybeUnary(e,t){return!this.hasPlugin("jsx")&&this.match(47)?this.tsParseTypeAssertion():super.parseMaybeUnary(e,t)}parseArrow(e){if(this.match(14)){let t=this.tryParse(e=>{let t=this.tsParseTypeOrTypePredicateAnnotation(14);return(this.canInsertSemicolon()||!this.match(19))&&e(),t});if(t.aborted)return;t.thrown||(t.error&&(this.state=t.failState),e.returnType=t.node)}return super.parseArrow(e)}parseFunctionParamType(e){this.eat(17)&&(e.optional=!0);let t=this.tsTryParseTypeAnnotation();return t&&(e.typeAnnotation=t),this.resetEndLocation(e),e}isAssignable(e,t){switch(e.type){case"TSTypeCastExpression":return this.isAssignable(e.expression,t);case"TSParameterProperty":return!0;default:return super.isAssignable(e,t)}}toAssignable(e,t=!1){switch(e.type){case"ParenthesizedExpression":this.toAssignableParenthesizedExpression(e,t);break;case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":t?this.expressionScope.recordArrowParameterBindingError(e1.UnexpectedTypeCastInParameter,e):this.raise(e1.UnexpectedTypeCastInParameter,e),this.toAssignable(e.expression,t);break;case"AssignmentExpression":t||"TSTypeCastExpression"!==e.left.type||(e.left=this.typeCastToParameter(e.left));default:super.toAssignable(e,t)}}toAssignableParenthesizedExpression(e,t){switch(e.expression.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":case"ParenthesizedExpression":this.toAssignable(e.expression,t);break;default:super.toAssignable(e,t)}}checkToRestConversion(e,t){switch(e.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSNonNullExpression":this.checkToRestConversion(e.expression,!1);break;default:super.checkToRestConversion(e,t)}}isValidLVal(e,t,s){switch(e){case"TSTypeCastExpression":return!0;case"TSParameterProperty":return"parameter";case"TSNonNullExpression":return"expression";case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":return(64!==s||!t)&&["expression",!0];default:return super.isValidLVal(e,t,s)}}parseBindingAtom(){return 78===this.state.type?this.parseIdentifier(!0):super.parseBindingAtom()}parseMaybeDecoratorArguments(e,t){if(this.match(47)||this.match(51)){let s=this.tsParseTypeArgumentsInExpression();if(this.match(10)){let i=super.parseMaybeDecoratorArguments(e,t);return i.typeParameters=s,i}this.unexpected(null,10)}return super.parseMaybeDecoratorArguments(e,t)}checkCommaAfterRest(e){return this.state.isAmbientContext&&this.match(12)&&this.lookaheadCharCode()===e?(this.next(),!1):super.checkCommaAfterRest(e)}isClassMethod(){return this.match(47)||super.isClassMethod()}isClassProperty(){return this.match(35)||this.match(14)||super.isClassProperty()}parseMaybeDefault(e,t){let s=super.parseMaybeDefault(e,t);return"AssignmentPattern"===s.type&&s.typeAnnotation&&s.right.start<s.typeAnnotation.start&&this.raise(e1.TypeAnnotationAfterAssign,s.typeAnnotation),s}getTokenFromCode(e){if(this.state.inType){if(62===e)return void this.finishOp(48,1);if(60===e)return void this.finishOp(47,1)}super.getTokenFromCode(e)}reScan_lt_gt(){let{type:e}=this.state;47===e?(this.state.pos-=1,this.readToken_lt()):48===e&&(this.state.pos-=1,this.readToken_gt())}reScan_lt(){let{type:e}=this.state;return 51===e?(this.state.pos-=2,this.finishOp(47,1),47):e}toAssignableListItem(e,t,s){let i=e[t];"TSTypeCastExpression"===i.type&&(e[t]=this.typeCastToParameter(i)),super.toAssignableListItem(e,t,s)}typeCastToParameter(e){return e.expression.typeAnnotation=e.typeAnnotation,this.resetEndLocation(e.expression,e.typeAnnotation.loc.end),e.expression}shouldParseArrow(e){return this.match(14)?e.every(e=>this.isAssignable(e,!0)):super.shouldParseArrow(e)}shouldParseAsyncArrow(){return this.match(14)||super.shouldParseAsyncArrow()}canHaveLeadingDecorator(){return super.canHaveLeadingDecorator()||this.isAbstractClass()}jsxParseOpeningElementAfterName(e){if(this.match(47)||this.match(51)){let t=this.tsTryParseAndCatch(()=>this.tsParseTypeArgumentsInExpression());t&&(e.typeParameters=t)}return super.jsxParseOpeningElementAfterName(e)}getGetterSetterExpectedParamCount(e){let t=super.getGetterSetterExpectedParamCount(e),s=this.getObjectOrClassMethodParams(e)[0];return s&&this.isThisParam(s)?t+1:t}parseCatchClauseParam(){let e=super.parseCatchClauseParam(),t=this.tsTryParseTypeAnnotation();return t&&(e.typeAnnotation=t,this.resetEndLocation(e)),e}tsInAmbientContext(e){let{isAmbientContext:t,strict:s}=this.state;this.state.isAmbientContext=!0,this.state.strict=!1;try{return e()}finally{this.state.isAmbientContext=t,this.state.strict=s}}parseClass(e,t,s){let i=this.state.inAbstractClass;this.state.inAbstractClass=!!e.abstract;try{return super.parseClass(e,t,s)}finally{this.state.inAbstractClass=i}}tsParseAbstractDeclaration(e,t){if(this.match(80))return e.abstract=!0,this.maybeTakeDecorators(t,this.parseClass(e,!0,!1));if(this.isContextual(129)){if(!this.hasFollowingLineBreak())return e.abstract=!0,this.raise(e1.NonClassMethodPropertyHasAbstractModifer,e),this.tsParseInterfaceDeclaration(e)}else this.unexpected(null,80)}parseMethod(e,t,s,i,r,a,n){let o=super.parseMethod(e,t,s,i,r,a,n);if(o.abstract&&(this.hasPlugin("estree")?o.value:o).body){let{key:e}=o;this.raise(e1.AbstractMethodHasImplementation,o,{methodName:"Identifier"!==e.type||o.computed?`[${this.input.slice(this.offsetToSourcePos(e.start),this.offsetToSourcePos(e.end))}]`:e.name})}return o}tsParseTypeParameterName(){return this.parseIdentifier().name}shouldParseAsAmbientContext(){return!!this.getPluginOption("typescript","dts")}parse(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),super.parse()}getExpression(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),super.getExpression()}parseExportSpecifier(e,t,s,i){return!t&&i?(this.parseTypeOnlyImportExportSpecifier(e,!1,s),this.finishNode(e,"ExportSpecifier")):(e.exportKind="value",super.parseExportSpecifier(e,t,s,i))}parseImportSpecifier(e,t,s,i,r){return!t&&i?(this.parseTypeOnlyImportExportSpecifier(e,!0,s),this.finishNode(e,"ImportSpecifier")):(e.importKind="value",super.parseImportSpecifier(e,t,s,i,s?4098:4096))}parseTypeOnlyImportExportSpecifier(e,t,s){let i,r=t?"imported":"local",a=t?"local":"exported",n=e[r],o=!1,h=!0,l=n.loc.start;if(this.isContextual(93)){let e=this.parseIdentifier();if(this.isContextual(93)){let s=this.parseIdentifier();O(this.state.type)?(o=!0,n=e,i=t?this.parseIdentifier():this.parseModuleExportName()):i=s,h=!1}else O(this.state.type)?(h=!1,i=t?this.parseIdentifier():this.parseModuleExportName()):(o=!0,n=e)}else O(this.state.type)&&(o=!0,t?(n=this.parseIdentifier(!0),this.isContextual(93)||this.checkReservedWord(n.name,n.loc.start,!0,!0)):n=this.parseModuleExportName());o&&s&&this.raise(t?e1.TypeModifierIsUsedInTypeImports:e1.TypeModifierIsUsedInTypeExports,l),e[r]=n,e[a]=i,e[t?"importKind":"exportKind"]=o?"type":"value",h&&this.eatContextual(93)&&(e[a]=t?this.parseIdentifier():this.parseModuleExportName()),e[a]||(e[a]=eU(e[r])),t&&this.checkIdentifier(e[a],o?4098:4096)}},v8intrinsic:e=>class extends e{parseV8Intrinsic(){if(this.match(54)){let e=this.state.startLoc,t=this.startNode();if(this.next(),D(this.state.type)){let e=this.parseIdentifierName(),s=this.createIdentifier(t,e);if(s.type="V8IntrinsicIdentifier",this.match(10))return s}this.unexpected(e)}}parseExprAtom(e){return this.parseV8Intrinsic()||super.parseExprAtom(e)}},placeholders:e=>class extends e{parsePlaceholder(e){if(this.match(133)){let t=this.startNode();return this.next(),this.assertNoSpace(),t.name=super.parseIdentifier(!0),this.assertNoSpace(),this.expect(133),this.finishPlaceholder(t,e)}}finishPlaceholder(e,t){let s=e;return s.expectedNode&&s.type||(s=this.finishNode(s,"Placeholder")),s.expectedNode=t,s}getTokenFromCode(e){37===e&&37===this.input.charCodeAt(this.state.pos+1)?this.finishOp(133,2):super.getTokenFromCode(e)}parseExprAtom(e){return this.parsePlaceholder("Expression")||super.parseExprAtom(e)}parseIdentifier(e){return this.parsePlaceholder("Identifier")||super.parseIdentifier(e)}checkReservedWord(e,t,s,i){void 0!==e&&super.checkReservedWord(e,t,s,i)}parseBindingAtom(){return this.parsePlaceholder("Pattern")||super.parseBindingAtom()}isValidLVal(e,t,s){return"Placeholder"===e||super.isValidLVal(e,t,s)}toAssignable(e,t){e&&"Placeholder"===e.type&&"Expression"===e.expectedNode?e.expectedNode="Pattern":super.toAssignable(e,t)}chStartsBindingIdentifier(e,t){return!!super.chStartsBindingIdentifier(e,t)||133===this.lookahead().type}verifyBreakContinue(e,t){e.label&&"Placeholder"===e.label.type||super.verifyBreakContinue(e,t)}parseExpressionStatement(e,t){var s;return"Placeholder"!==t.type||null!=(s=t.extra)&&s.parenthesized?super.parseExpressionStatement(e,t):this.match(14)?(e.label=this.finishPlaceholder(t,"Identifier"),this.next(),e.body=super.parseStatementOrSloppyAnnexBFunctionDeclaration(),this.finishNode(e,"LabeledStatement")):(this.semicolon(),e.name=t.name,this.finishPlaceholder(e,"Statement"))}parseBlock(e,t,s){return this.parsePlaceholder("BlockStatement")||super.parseBlock(e,t,s)}parseFunctionId(e){return this.parsePlaceholder("Identifier")||super.parseFunctionId(e)}parseClass(e,t,s){let i=t?"ClassDeclaration":"ClassExpression";this.next();let r=this.state.strict,a=this.parsePlaceholder("Identifier");if(a)if(this.match(81)||this.match(133)||this.match(5))e.id=a;else if(s||!t)return e.id=null,e.body=this.finishPlaceholder(a,"ClassBody"),this.finishNode(e,i);else throw this.raise(e4.ClassNameIsRequired,this.state.startLoc);else this.parseClassId(e,t,s);return super.parseClassSuper(e),e.body=this.parsePlaceholder("ClassBody")||super.parseClassBody(!!e.superClass,r),this.finishNode(e,i)}parseExport(e,t){let s=this.parsePlaceholder("Identifier");if(!s)return super.parseExport(e,t);if(!this.isContextual(98)&&!this.match(12))return e.specifiers=[],e.source=null,e.declaration=this.finishPlaceholder(s,"Declaration"),this.finishNode(e,"ExportNamedDeclaration");this.expectPlugin("exportDefaultFrom");let i=this.startNode();return i.exported=s,e.specifiers=[this.finishNode(i,"ExportDefaultSpecifier")],super.parseExport(e,t)}isExportDefaultSpecifier(){if(this.match(65)){let e=this.nextTokenStart();if(this.isUnparsedContextual(e,"from")&&this.input.startsWith(I[133],this.nextTokenStartSince(e+4)))return!0}return super.isExportDefaultSpecifier()}maybeParseExportDefaultSpecifier(e,t){var s;return null!=(s=e.specifiers)&&!!s.length||super.maybeParseExportDefaultSpecifier(e,t)}checkExport(e){let{specifiers:t}=e;null!=t&&t.length&&(e.specifiers=t.filter(e=>"Placeholder"===e.exported.type)),super.checkExport(e),e.specifiers=t}parseImport(e){let t=this.parsePlaceholder("Identifier");if(!t)return super.parseImport(e);if(e.specifiers=[],!this.isContextual(98)&&!this.match(12))return e.source=this.finishPlaceholder(t,"StringLiteral"),this.semicolon(),this.finishNode(e,"ImportDeclaration");let s=this.startNodeAtNode(t);return s.local=t,e.specifiers.push(this.finishNode(s,"ImportDefaultSpecifier")),this.eat(12)&&(this.maybeParseStarImportSpecifier(e)||this.parseNamedImportSpecifiers(e)),this.expectContextual(98),e.source=this.parseImportSource(),this.semicolon(),this.finishNode(e,"ImportDeclaration")}parseImportSource(){return this.parsePlaceholder("StringLiteral")||super.parseImportSource()}assertNoSpace(){this.state.start>this.offsetToSourcePos(this.state.lastTokEndLoc.index)&&this.raise(e4.UnexpectedSpace,this.state.lastTokEndLoc)}}},e6=Object.keys(e8);class e9 extends eQ{checkProto(e,t,s,i){if("SpreadElement"===e.type||this.isObjectMethod(e)||e.computed||e.shorthand)return s;let r=e.key;return"__proto__"===("Identifier"===r.type?r.name:r.value)?(t?this.raise(d.RecordNoProto,r):s&&(i?null===i.doubleProtoLoc&&(i.doubleProtoLoc=r.loc.start):this.raise(d.DuplicateProto,r)),!0):s}shouldExitDescending(e,t){return"ArrowFunctionExpression"===e.type&&this.offsetToSourcePos(e.start)===t}getExpression(){this.enterInitialScopes(),this.nextToken();let e=this.parseExpression();return this.match(140)||this.unexpected(),this.finalizeRemainingComments(),e.comments=this.comments,e.errors=this.state.errors,256&this.optionFlags&&(e.tokens=this.tokens),e}parseExpression(e,t){return e?this.disallowInAnd(()=>this.parseExpressionBase(t)):this.allowInAnd(()=>this.parseExpressionBase(t))}parseExpressionBase(e){let t=this.state.startLoc,s=this.parseMaybeAssign(e);if(this.match(12)){let i=this.startNodeAt(t);for(i.expressions=[s];this.eat(12);)i.expressions.push(this.parseMaybeAssign(e));return this.toReferencedList(i.expressions),this.finishNode(i,"SequenceExpression")}return s}parseMaybeAssignDisallowIn(e,t){return this.disallowInAnd(()=>this.parseMaybeAssign(e,t))}parseMaybeAssignAllowIn(e,t){return this.allowInAnd(()=>this.parseMaybeAssign(e,t))}setOptionalParametersError(e){e.optionalParametersLoc=this.state.startLoc}parseMaybeAssign(e,t){var s;let i,r=this.state.startLoc,a=this.isContextual(108);if(a&&this.prodParam.hasYield){this.next();let e=this.parseYield(r);return t&&(e=t.call(this,e,r)),e}e?i=!1:(e=new eF,i=!0);let{type:n}=this.state;(10===n||D(n))&&(this.state.potentialArrowAt=this.state.start);let o=this.parseMaybeConditional(e);if(t&&(o=t.call(this,o,r)),(s=this.state.type)>=29&&s<=33){let t=this.startNodeAt(r);if(t.operator=this.state.value,this.match(29)){this.toAssignable(o,!0),t.left=o;let s=r.index;null!=e.doubleProtoLoc&&e.doubleProtoLoc.index>=s&&(e.doubleProtoLoc=null),null!=e.shorthandAssignLoc&&e.shorthandAssignLoc.index>=s&&(e.shorthandAssignLoc=null),null!=e.privateKeyLoc&&e.privateKeyLoc.index>=s&&(this.checkDestructuringPrivate(e),e.privateKeyLoc=null)}else t.left=o;return this.next(),t.right=this.parseMaybeAssign(),this.checkLVal(o,this.finishNode(t,"AssignmentExpression")),t}if(i&&this.checkExpressionErrors(e,!0),a){let{type:e}=this.state;if((this.hasPlugin("v8intrinsic")?N[e]:N[e]&&!this.match(54))&&!this.isAmbiguousPrefixOrIdentifier())return this.raiseOverwrite(d.YieldNotInGeneratorFunction,r),this.parseYield(r)}return o}parseMaybeConditional(e){let t=this.state.startLoc,s=this.state.potentialArrowAt,i=this.parseExprOps(e);return this.shouldExitDescending(i,s)?i:this.parseConditional(i,t,e)}parseConditional(e,t,s){if(this.eat(17)){let s=this.startNodeAt(t);return s.test=e,s.consequent=this.parseMaybeAssignAllowIn(),this.expect(14),s.alternate=this.parseMaybeAssign(),this.finishNode(s,"ConditionalExpression")}return e}parseMaybeUnaryOrPrivate(e){return this.match(139)?this.parsePrivateName():this.parseMaybeUnary(e)}parseExprOps(e){let t=this.state.startLoc,s=this.state.potentialArrowAt,i=this.parseMaybeUnaryOrPrivate(e);return this.shouldExitDescending(i,s)?i:this.parseExprOp(i,t,-1)}parseExprOp(e,t,s){if(this.isPrivateName(e)){let t=this.getPrivateNameSV(e);!(s>=C[58])&&this.prodParam.hasIn&&this.match(58)||this.raise(d.PrivateInExpectedIn,e,{identifierName:t}),this.classScope.usePrivateName(t,e.loc.start)}let i=this.state.type;if(i>=39&&i<=59&&(this.prodParam.hasIn||!this.match(58))){let r=C[i];if(r>s){if(39===i){if(this.expectPlugin("pipelineOperator"),this.state.inFSharpPipelineDirectBody)return e;this.checkPipelineAtInfixOperator(e,t)}let a=this.startNodeAt(t);a.left=e,a.operator=this.state.value;let n=41===i||42===i,o=40===i;if(o&&(r=C[42]),this.next(),39===i&&this.hasPlugin(["pipelineOperator",{proposal:"minimal"}])&&96===this.state.type&&this.prodParam.hasAwait)throw this.raise(d.UnexpectedAwaitAfterPipelineBody,this.state.startLoc);a.right=this.parseExprOpRightExpr(i,r);let h=this.finishNode(a,n||o?"LogicalExpression":"BinaryExpression"),l=this.state.type;if(o&&(41===l||42===l)||n&&40===l)throw this.raise(d.MixingCoalesceWithLogical,this.state.startLoc);return this.parseExprOp(h,t,s)}}return e}parseExprOpRightExpr(e,t){let s=this.state.startLoc;if(39===e){switch(this.getPluginOption("pipelineOperator","proposal")){case"hack":return this.withTopicBindingContext(()=>this.parseHackPipeBody());case"fsharp":return this.withSoloAwaitPermittingContext(()=>this.parseFSharpPipelineBody(t))}if("smart"===this.getPluginOption("pipelineOperator","proposal"))return this.withTopicBindingContext(()=>{if(this.prodParam.hasYield&&this.isContextual(108))throw this.raise(d.PipeBodyIsTighter,this.state.startLoc);return this.parseSmartPipelineBodyInStyle(this.parseExprOpBaseRightExpr(e,t),s)})}return this.parseExprOpBaseRightExpr(e,t)}parseExprOpBaseRightExpr(e,t){let s=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnaryOrPrivate(),s,57===e?t-1:t)}parseHackPipeBody(){var e;let{startLoc:t}=this.state,s=this.parseMaybeAssign();return h.has(s.type)&&!(null!=(e=s.extra)&&e.parenthesized)&&this.raise(d.PipeUnparenthesizedBody,t,{type:s.type}),this.topicReferenceWasUsedInCurrentContext()||this.raise(d.PipeTopicUnused,t),s}checkExponentialAfterUnary(e){this.match(57)&&this.raise(d.UnexpectedTokenUnaryExponentiation,e.argument)}parseMaybeUnary(e,t){let s=this.state.startLoc,i=this.isContextual(96);if(i&&this.recordAwaitIfAllowed()){this.next();let e=this.parseAwait(s);return t||this.checkExponentialAfterUnary(e),e}let r=this.match(34),a=this.startNode();if(v[this.state.type]){a.operator=this.state.value,a.prefix=!0,this.match(72)&&this.expectPlugin("throwExpressions");let s=this.match(89);if(this.next(),a.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(e,!0),this.state.strict&&s){let e=a.argument;"Identifier"===e.type?this.raise(d.StrictDelete,a):this.hasPropertyAsPrivateName(e)&&this.raise(d.DeletePrivateField,a)}if(!r)return t||this.checkExponentialAfterUnary(a),this.finishNode(a,"UnaryExpression")}let n=this.parseUpdate(a,r,e);if(i){let{type:e}=this.state;if((this.hasPlugin("v8intrinsic")?N[e]:N[e]&&!this.match(54))&&!this.isAmbiguousPrefixOrIdentifier())return this.raiseOverwrite(d.AwaitNotInAsyncContext,s),this.parseAwait(s)}return n}parseUpdate(e,t,s){if(t)return this.checkLVal(e.argument,this.finishNode(e,"UpdateExpression")),e;let i=this.state.startLoc,r=this.parseExprSubscripts(s);if(this.checkExpressionErrors(s,!1))return r;for(;34===this.state.type&&!this.canInsertSemicolon();){let e=this.startNodeAt(i);e.operator=this.state.value,e.prefix=!1,e.argument=r,this.next(),this.checkLVal(r,r=this.finishNode(e,"UpdateExpression"))}return r}parseExprSubscripts(e){let t=this.state.startLoc,s=this.state.potentialArrowAt,i=this.parseExprAtom(e);return this.shouldExitDescending(i,s)?i:this.parseSubscripts(i,t)}parseSubscripts(e,t,s){let i={optionalChainMember:!1,maybeAsyncArrow:this.atPossibleAsyncArrow(e),stop:!1};do e=this.parseSubscript(e,t,s,i),i.maybeAsyncArrow=!1;while(!i.stop);return e}parseSubscript(e,t,s,i){let{type:r}=this.state;if(!s&&15===r)return this.parseBind(e,t,s,i);if(U(r))return this.parseTaggedTemplateExpression(e,t,i);let a=!1;if(18===r){if(s&&(this.raise(d.OptionalChainingNoNew,this.state.startLoc),40===this.lookaheadCharCode()))return i.stop=!0,e;i.optionalChainMember=a=!0,this.next()}if(!s&&this.match(10))return this.parseCoverCallAndAsyncArrowHead(e,t,i,a);{let s=this.eat(0);return s||a||this.eat(16)?this.parseMember(e,t,i,s,a):(i.stop=!0,e)}}parseMember(e,t,s,i,r){let a=this.startNodeAt(t);return(a.object=e,a.computed=i,i?(a.property=this.parseExpression(),this.expect(3)):this.match(139)?("Super"===e.type&&this.raise(d.SuperPrivateField,t),this.classScope.usePrivateName(this.state.value,this.state.startLoc),a.property=this.parsePrivateName()):a.property=this.parseIdentifier(!0),s.optionalChainMember)?(a.optional=r,this.finishNode(a,"OptionalMemberExpression")):this.finishNode(a,"MemberExpression")}parseBind(e,t,s,i){let r=this.startNodeAt(t);return r.object=e,this.next(),r.callee=this.parseNoCallExpr(),i.stop=!0,this.parseSubscripts(this.finishNode(r,"BindExpression"),t,s)}parseCoverCallAndAsyncArrowHead(e,t,s,i){let r=this.state.maybeInArrowParameters,a=null;this.state.maybeInArrowParameters=!0,this.next();let n=this.startNodeAt(t);n.callee=e;let{maybeAsyncArrow:o,optionalChainMember:h}=s;o&&(this.expressionScope.enter(new ek(2)),a=new eF),h&&(n.optional=i),i?n.arguments=this.parseCallExpressionArguments(11):n.arguments=this.parseCallExpressionArguments(11,"Super"!==e.type,n,a);let l=this.finishCallExpression(n,h);return o&&this.shouldParseAsyncArrow()&&!i?(s.stop=!0,this.checkDestructuringPrivate(a),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),l=this.parseAsyncArrowFromCallExpression(this.startNodeAt(t),l)):(o&&(this.checkExpressionErrors(a,!0),this.expressionScope.exit()),this.toReferencedArguments(l)),this.state.maybeInArrowParameters=r,l}toReferencedArguments(e,t){this.toReferencedListDeep(e.arguments,t)}parseTaggedTemplateExpression(e,t,s){let i=this.startNodeAt(t);return i.tag=e,i.quasi=this.parseTemplate(!0),s.optionalChainMember&&this.raise(d.OptionalChainingNoTemplate,t),this.finishNode(i,"TaggedTemplateExpression")}atPossibleAsyncArrow(e){return"Identifier"===e.type&&"async"===e.name&&this.state.lastTokEndLoc.index===e.end&&!this.canInsertSemicolon()&&e.end-e.start==5&&this.offsetToSourcePos(e.start)===this.state.potentialArrowAt}finishCallExpression(e,t){if("Import"===e.callee.type)if(0===e.arguments.length||e.arguments.length>2)this.raise(d.ImportCallArity,e);else for(let t of e.arguments)"SpreadElement"===t.type&&this.raise(d.ImportCallSpreadArgument,t);return this.finishNode(e,t?"OptionalCallExpression":"CallExpression")}parseCallExpressionArguments(e,t,s,i){let r=[],a=!0,n=this.state.inFSharpPipelineDirectBody;for(this.state.inFSharpPipelineDirectBody=!1;!this.eat(e);){if(a)a=!1;else if(this.expect(12),this.match(e)){s&&this.addTrailingCommaExtraToNode(s),this.next();break}r.push(this.parseExprListItem(!1,i,t))}return this.state.inFSharpPipelineDirectBody=n,r}shouldParseAsyncArrow(){return this.match(19)&&!this.canInsertSemicolon()}parseAsyncArrowFromCallExpression(e,t){var s;return this.resetPreviousNodeTrailingComments(t),this.expect(19),this.parseArrowExpression(e,t.arguments,!0,null==(s=t.extra)?void 0:s.trailingCommaLoc),t.innerComments&&eh(e,t.innerComments),t.callee.trailingComments&&eh(e,t.callee.trailingComments),e}parseNoCallExpr(){let e=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),e,!0)}parseExprAtom(e){let t,s=null,{type:i}=this.state;switch(i){case 79:return this.parseSuper();case 83:if(t=this.startNode(),this.next(),this.match(16))return this.parseImportMetaProperty(t);if(!this.match(10))return this.raise(d.UnsupportedImport,this.state.lastTokStartLoc),this.finishNode(t,"Import");if(512&this.optionFlags)return this.parseImportCall(t);return this.finishNode(t,"Import");case 78:return t=this.startNode(),this.next(),this.finishNode(t,"ThisExpression");case 90:return this.parseDo(this.startNode(),!1);case 56:case 31:return this.readRegexp(),this.parseRegExpLiteral(this.state.value);case 135:return this.parseNumericLiteral(this.state.value);case 136:return this.parseBigIntLiteral(this.state.value);case 134:return this.parseStringLiteral(this.state.value);case 84:return this.parseNullLiteral();case 85:return this.parseBooleanLiteral(!0);case 86:return this.parseBooleanLiteral(!1);case 10:{let e=this.state.potentialArrowAt===this.state.start;return this.parseParenAndDistinguishExpression(e)}case 2:case 1:return this.parseArrayLike(2===this.state.type?4:3,!1,!0);case 0:return this.parseArrayLike(3,!0,!1,e);case 6:case 7:return this.parseObjectLike(6===this.state.type?9:8,!1,!0);case 5:return this.parseObjectLike(8,!1,!1,e);case 68:return this.parseFunctionOrFunctionSent();case 26:s=this.parseDecorators();case 80:return this.parseClass(this.maybeTakeDecorators(s,this.startNode()),!1);case 77:return this.parseNewOrNewTarget();case 25:case 24:return this.parseTemplate(!1);case 15:{t=this.startNode(),this.next(),t.object=null;let e=t.callee=this.parseNoCallExpr();if("MemberExpression"===e.type)return this.finishNode(t,"BindExpression");throw this.raise(d.UnsupportedBind,e)}case 139:return this.raise(d.PrivateInExpectedIn,this.state.startLoc,{identifierName:this.state.value}),this.parsePrivateName();case 33:return this.parseTopicReferenceThenEqualsSign(54,"%");case 32:return this.parseTopicReferenceThenEqualsSign(44,"^");case 37:case 38:return this.parseTopicReference("hack");case 44:case 54:case 27:{let e=this.getPluginOption("pipelineOperator","proposal");if(e)return this.parseTopicReference(e);this.unexpected();break}case 47:{let e=this.input.codePointAt(this.nextTokenStart());K(e)||62===e?this.expectOnePlugin(["jsx","flow","typescript"]):this.unexpected();break}default:if(137===i)return this.parseDecimalLiteral(this.state.value);if(D(i)){if(this.isContextual(127)&&123===this.lookaheadInLineCharCode())return this.parseModuleExpression();let e=this.state.potentialArrowAt===this.state.start,t=this.state.containsEsc,s=this.parseIdentifier();if(!t&&"async"===s.name&&!this.canInsertSemicolon()){let{type:e}=this.state;if(68===e)return this.resetPreviousNodeTrailingComments(s),this.next(),this.parseAsyncFunctionExpression(this.startNodeAtNode(s));if(D(e))if(61===this.lookaheadCharCode())return this.parseAsyncArrowUnaryFunction(this.startNodeAtNode(s));else return s;if(90===e)return this.resetPreviousNodeTrailingComments(s),this.parseDo(this.startNodeAtNode(s),!0)}if(e&&this.match(19)&&!this.canInsertSemicolon())return this.next(),this.parseArrowExpression(this.startNodeAtNode(s),[s],!1);return s}this.unexpected()}}parseTopicReferenceThenEqualsSign(e,t){let s=this.getPluginOption("pipelineOperator","proposal");if(s)return this.state.type=e,this.state.value=t,this.state.pos--,this.state.end--,this.state.endLoc=r(this.state.endLoc,-1),this.parseTopicReference(s);this.unexpected()}parseTopicReference(e){let t=this.startNode(),s=this.state.startLoc,i=this.state.type;return this.next(),this.finishTopicReference(t,s,e,i)}finishTopicReference(e,t,s,i){if(this.testTopicReferenceConfiguration(s,t,i))if("hack"===s)return this.topicReferenceIsAllowedInCurrentContext()||this.raise(d.PipeTopicUnbound,t),this.registerTopicReference(),this.finishNode(e,"TopicReference");else return this.topicReferenceIsAllowedInCurrentContext()||this.raise(d.PrimaryTopicNotAllowed,t),this.registerTopicReference(),this.finishNode(e,"PipelinePrimaryTopicReference");throw this.raise(d.PipeTopicUnconfiguredToken,t,{token:I[i]})}testTopicReferenceConfiguration(e,t,s){switch(e){case"hack":return this.hasPlugin(["pipelineOperator",{topicToken:I[s]}]);case"smart":return 27===s;default:throw this.raise(d.PipeTopicRequiresHackPipes,t)}}parseAsyncArrowUnaryFunction(e){this.prodParam.enter(eD(!0,this.prodParam.hasYield));let t=[this.parseIdentifier()];return this.prodParam.exit(),this.hasPrecedingLineBreak()&&this.raise(d.LineTerminatorBeforeArrow,this.state.curPosition()),this.expect(19),this.parseArrowExpression(e,t,!0)}parseDo(e,t){this.expectPlugin("doExpressions"),t&&this.expectPlugin("asyncDoExpressions"),e.async=t,this.next();let s=this.state.labels;return this.state.labels=[],t?(this.prodParam.enter(2),e.body=this.parseBlock(),this.prodParam.exit()):e.body=this.parseBlock(),this.state.labels=s,this.finishNode(e,"DoExpression")}parseSuper(){let e=this.startNode();return this.next(),!this.match(10)||this.scope.allowDirectSuper||16&this.optionFlags?this.scope.allowSuper||16&this.optionFlags||this.raise(d.UnexpectedSuper,e):this.raise(d.SuperNotAllowed,e),this.match(10)||this.match(0)||this.match(16)||this.raise(d.UnsupportedSuper,e),this.finishNode(e,"Super")}parsePrivateName(){let e=this.startNode(),t=this.startNodeAt(r(this.state.startLoc,1)),s=this.state.value;return this.next(),e.id=this.createIdentifier(t,s),this.finishNode(e,"PrivateName")}parseFunctionOrFunctionSent(){let e=this.startNode();if(this.next(),this.prodParam.hasYield&&this.match(16)){let t=this.createIdentifier(this.startNodeAtNode(e),"function");return this.next(),this.match(103)?this.expectPlugin("functionSent"):this.hasPlugin("functionSent")||this.unexpected(),this.parseMetaProperty(e,t,"sent")}return this.parseFunction(e)}parseMetaProperty(e,t,s){e.meta=t;let i=this.state.containsEsc;return e.property=this.parseIdentifier(!0),(e.property.name!==s||i)&&this.raise(d.UnsupportedMetaProperty,e.property,{target:t.name,onlyValidPropertyName:s}),this.finishNode(e,"MetaProperty")}parseImportMetaProperty(e){let t=this.createIdentifier(this.startNodeAtNode(e),"import");if(this.next(),this.isContextual(101))this.inModule||this.raise(d.ImportMetaOutsideModule,t),this.sawUnambiguousESM=!0;else if(this.isContextual(105)||this.isContextual(97)){let t=this.isContextual(105);if(this.expectPlugin(t?"sourcePhaseImports":"deferredImportEvaluation"),!(512&this.optionFlags))throw this.raise(d.DynamicImportPhaseRequiresImportExpressions,this.state.startLoc,{phase:this.state.value});return this.next(),e.phase=t?"source":"defer",this.parseImportCall(e)}return this.parseMetaProperty(e,t,"meta")}parseLiteralAtNode(e,t,s){return this.addExtra(s,"rawValue",e),this.addExtra(s,"raw",this.input.slice(this.offsetToSourcePos(s.start),this.state.end)),s.value=e,this.next(),this.finishNode(s,t)}parseLiteral(e,t){let s=this.startNode();return this.parseLiteralAtNode(e,t,s)}parseStringLiteral(e){return this.parseLiteral(e,"StringLiteral")}parseNumericLiteral(e){return this.parseLiteral(e,"NumericLiteral")}parseBigIntLiteral(e){return this.parseLiteral(e,"BigIntLiteral")}parseDecimalLiteral(e){return this.parseLiteral(e,"DecimalLiteral")}parseRegExpLiteral(e){let t=this.startNode();return this.addExtra(t,"raw",this.input.slice(this.offsetToSourcePos(t.start),this.state.end)),t.pattern=e.pattern,t.flags=e.flags,this.next(),this.finishNode(t,"RegExpLiteral")}parseBooleanLiteral(e){let t=this.startNode();return t.value=e,this.next(),this.finishNode(t,"BooleanLiteral")}parseNullLiteral(){let e=this.startNode();return this.next(),this.finishNode(e,"NullLiteral")}parseParenAndDistinguishExpression(e){let t,s,i,r=this.state.startLoc;this.next(),this.expressionScope.enter(new ek(1));let a=this.state.maybeInArrowParameters,n=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=!0,this.state.inFSharpPipelineDirectBody=!1;let o=this.state.startLoc,h=[],l=new eF,p=!0;for(;!this.match(11);){if(p)p=!1;else if(this.expect(12,null===l.optionalParametersLoc?null:l.optionalParametersLoc),this.match(11)){i=this.state.startLoc;break}if(this.match(21)){let e=this.state.startLoc;if(s=this.state.startLoc,h.push(this.parseParenItem(this.parseRestBinding(),e)),!this.checkCommaAfterRest(41))break}else h.push(this.parseMaybeAssignAllowIn(l,this.parseParenItem))}let c=this.state.lastTokEndLoc;this.expect(11),this.state.maybeInArrowParameters=a,this.state.inFSharpPipelineDirectBody=n;let u=this.startNodeAt(r);return e&&this.shouldParseArrow(h)&&(u=this.parseArrow(u))?(this.checkDestructuringPrivate(l),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),this.parseArrowExpression(u,h,!1),u):(this.expressionScope.exit(),h.length||this.unexpected(this.state.lastTokStartLoc),i&&this.unexpected(i),s&&this.unexpected(s),this.checkExpressionErrors(l,!0),this.toReferencedListDeep(h,!0),h.length>1?((t=this.startNodeAt(o)).expressions=h,this.finishNode(t,"SequenceExpression"),this.resetEndLocation(t,c)):t=h[0],this.wrapParenthesis(r,t))}wrapParenthesis(e,t){if(!(1024&this.optionFlags))return this.addExtra(t,"parenthesized",!0),this.addExtra(t,"parenStart",e.index),this.takeSurroundingComments(t,e.index,this.state.lastTokEndLoc.index),t;let s=this.startNodeAt(e);return s.expression=t,this.finishNode(s,"ParenthesizedExpression")}shouldParseArrow(e){return!this.canInsertSemicolon()}parseArrow(e){if(this.eat(19))return e}parseParenItem(e,t){return e}parseNewOrNewTarget(){let e=this.startNode();if(this.next(),this.match(16)){let t=this.createIdentifier(this.startNodeAtNode(e),"new");this.next();let s=this.parseMetaProperty(e,t,"target");return this.scope.inNonArrowFunction||this.scope.inClass||4&this.optionFlags||this.raise(d.UnexpectedNewTarget,s),s}return this.parseNew(e)}parseNew(e){if(this.parseNewCallee(e),this.eat(10)){let t=this.parseExprList(11);this.toReferencedList(t),e.arguments=t}else e.arguments=[];return this.finishNode(e,"NewExpression")}parseNewCallee(e){let t=this.match(83),s=this.parseNoCallExpr();e.callee=s,t&&("Import"===s.type||"ImportExpression"===s.type)&&this.raise(d.ImportCallNotNewExpression,s)}parseTemplateElement(e){let{start:t,startLoc:s,end:i,value:a}=this.state,n=this.startNodeAt(r(s,1));null!==a||e||this.raise(d.InvalidEscapeSequenceTemplate,r(this.state.firstInvalidTemplateEscapePos,1));let o=this.match(24),h=o?-1:-2,l=i+h;n.value={raw:this.input.slice(t+1,l).replace(/\r\n?/g,"\n"),cooked:null===a?null:a.slice(1,h)},n.tail=o,this.next();let p=this.finishNode(n,"TemplateElement");return this.resetEndLocation(p,r(this.state.lastTokEndLoc,h)),p}parseTemplate(e){let t=this.startNode(),s=this.parseTemplateElement(e),i=[s],r=[];for(;!s.tail;)r.push(this.parseTemplateSubstitution()),this.readTemplateContinuation(),i.push(s=this.parseTemplateElement(e));return t.expressions=r,t.quasis=i,this.finishNode(t,"TemplateLiteral")}parseTemplateSubstitution(){return this.parseExpression()}parseObjectLike(e,t,s,i){s&&this.expectPlugin("recordAndTuple");let r=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;let a=!1,n=!0,o=this.startNode();for(o.properties=[],this.next();!this.match(e);){let r;if(n)n=!1;else if(this.expect(12),this.match(e)){this.addTrailingCommaExtraToNode(o);break}t?r=this.parseBindingProperty():(r=this.parsePropertyDefinition(i),a=this.checkProto(r,s,a,i)),s&&!this.isObjectProperty(r)&&"SpreadElement"!==r.type&&this.raise(d.InvalidRecordProperty,r),r.shorthand&&this.addExtra(r,"shorthand",!0),o.properties.push(r)}this.next(),this.state.inFSharpPipelineDirectBody=r;let h="ObjectExpression";return t?h="ObjectPattern":s&&(h="RecordExpression"),this.finishNode(o,h)}addTrailingCommaExtraToNode(e){this.addExtra(e,"trailingComma",this.state.lastTokStartLoc.index),this.addExtra(e,"trailingCommaLoc",this.state.lastTokStartLoc,!1)}maybeAsyncOrAccessorProp(e){return!e.computed&&"Identifier"===e.key.type&&(this.isLiteralPropertyName()||this.match(0)||this.match(55))}parsePropertyDefinition(e){let t,s=[];if(this.match(26))for(this.hasPlugin("decorators")&&this.raise(d.UnsupportedPropertyDecorator,this.state.startLoc);this.match(26);)s.push(this.parseDecorator());let i=this.startNode(),r=!1,a=!1;if(this.match(21))return s.length&&this.unexpected(),this.parseSpread();s.length&&(i.decorators=s,s=[]),i.method=!1,e&&(t=this.state.startLoc);let n=this.eat(55);this.parsePropertyNamePrefixOperator(i);let o=this.state.containsEsc;if(this.parsePropertyName(i,e),!n&&!o&&this.maybeAsyncOrAccessorProp(i)){let{key:e}=i,t=e.name;"async"!==t||this.hasPrecedingLineBreak()||(r=!0,this.resetPreviousNodeTrailingComments(e),n=this.eat(55),this.parsePropertyName(i)),("get"===t||"set"===t)&&(a=!0,this.resetPreviousNodeTrailingComments(e),i.kind=t,this.match(55)&&(n=!0,this.raise(d.AccessorIsGenerator,this.state.curPosition(),{kind:t}),this.next()),this.parsePropertyName(i))}return this.parseObjPropValue(i,t,n,r,!1,a,e)}getGetterSetterExpectedParamCount(e){return+("get"!==e.kind)}getObjectOrClassMethodParams(e){return e.params}checkGetterSetterParams(e){var t;let s=this.getGetterSetterExpectedParamCount(e),i=this.getObjectOrClassMethodParams(e);i.length!==s&&this.raise("get"===e.kind?d.BadGetterArity:d.BadSetterArity,e),"set"===e.kind&&(null==(t=i[i.length-1])?void 0:t.type)==="RestElement"&&this.raise(d.BadSetterRestParameter,e)}parseObjectMethod(e,t,s,i,r){if(r){let s=this.parseMethod(e,t,!1,!1,!1,"ObjectMethod");return this.checkGetterSetterParams(s),s}if(s||t||this.match(10))return i&&this.unexpected(),e.kind="method",e.method=!0,this.parseMethod(e,t,s,!1,!1,"ObjectMethod")}parseObjectProperty(e,t,s,i){if(e.shorthand=!1,this.eat(14))return e.value=s?this.parseMaybeDefault(this.state.startLoc):this.parseMaybeAssignAllowIn(i),this.finishNode(e,"ObjectProperty");if(!e.computed&&"Identifier"===e.key.type){if(this.checkReservedWord(e.key.name,e.key.loc.start,!0,!1),s)e.value=this.parseMaybeDefault(t,eU(e.key));else if(this.match(29)){let s=this.state.startLoc;null!=i?null===i.shorthandAssignLoc&&(i.shorthandAssignLoc=s):this.raise(d.InvalidCoverInitializedName,s),e.value=this.parseMaybeDefault(t,eU(e.key))}else e.value=eU(e.key);return e.shorthand=!0,this.finishNode(e,"ObjectProperty")}}parseObjPropValue(e,t,s,i,r,a,n){let o=this.parseObjectMethod(e,s,i,r,a)||this.parseObjectProperty(e,t,r,n);return o||this.unexpected(),o}parsePropertyName(e,t){if(this.eat(0))e.computed=!0,e.key=this.parseMaybeAssignAllowIn(),this.expect(3);else{let s,{type:i,value:r}=this.state;if(O(i))s=this.parseIdentifier(!0);else switch(i){case 135:s=this.parseNumericLiteral(r);break;case 134:s=this.parseStringLiteral(r);break;case 136:s=this.parseBigIntLiteral(r);break;case 139:{let e=this.state.startLoc;null!=t?null===t.privateKeyLoc&&(t.privateKeyLoc=e):this.raise(d.UnexpectedPrivateField,e),s=this.parsePrivateName();break}default:if(137===i){s=this.parseDecimalLiteral(r);break}this.unexpected()}e.key=s,139!==i&&(e.computed=!1)}}initFunction(e,t){e.id=null,e.generator=!1,e.async=t}parseMethod(e,t,s,i,r,a,n=!1){this.initFunction(e,s),e.generator=t,this.scope.enter(18|64*!!n|32*!!r),this.prodParam.enter(eD(s,e.generator)),this.parseFunctionParams(e,i);let o=this.parseFunctionBodyAndFinish(e,a,!0);return this.prodParam.exit(),this.scope.exit(),o}parseArrayLike(e,t,s,i){s&&this.expectPlugin("recordAndTuple");let r=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;let a=this.startNode();return this.next(),a.elements=this.parseExprList(e,!s,i,a),this.state.inFSharpPipelineDirectBody=r,this.finishNode(a,s?"TupleExpression":"ArrayExpression")}parseArrowExpression(e,t,s,i){this.scope.enter(6);let r=eD(s,!1);!this.match(5)&&this.prodParam.hasIn&&(r|=8),this.prodParam.enter(r),this.initFunction(e,s);let a=this.state.maybeInArrowParameters;return t&&(this.state.maybeInArrowParameters=!0,this.setArrowFunctionParameters(e,t,i)),this.state.maybeInArrowParameters=!1,this.parseFunctionBody(e,!0),this.prodParam.exit(),this.scope.exit(),this.state.maybeInArrowParameters=a,this.finishNode(e,"ArrowFunctionExpression")}setArrowFunctionParameters(e,t,s){this.toAssignableList(t,s,!1),e.params=t}parseFunctionBodyAndFinish(e,t,s=!1){return this.parseFunctionBody(e,!1,s),this.finishNode(e,t)}parseFunctionBody(e,t,s=!1){let i=t&&!this.match(5);if(this.expressionScope.enter(new ev),i)e.body=this.parseMaybeAssign(),this.checkParams(e,!1,t,!1);else{let i=this.state.strict,r=this.state.labels;this.state.labels=[],this.prodParam.enter(4|this.prodParam.currentFlags()),e.body=this.parseBlock(!0,!1,r=>{let a=!this.isSimpleParamList(e.params);r&&a&&this.raise(d.IllegalLanguageModeDirective,("method"===e.kind||"constructor"===e.kind)&&e.key?e.key.loc.end:e);let n=!i&&this.state.strict;this.checkParams(e,!this.state.strict&&!t&&!s&&!a,t,n),this.state.strict&&e.id&&this.checkIdentifier(e.id,65,n)}),this.prodParam.exit(),this.state.labels=r}this.expressionScope.exit()}isSimpleParameter(e){return"Identifier"===e.type}isSimpleParamList(e){for(let t=0,s=e.length;t<s;t++)if(!this.isSimpleParameter(e[t]))return!1;return!0}checkParams(e,t,s,i=!0){let r=!t&&new Set,a={type:"FormalParameters"};for(let t of e.params)this.checkLVal(t,a,5,r,i)}parseExprList(e,t,s,i){let r=[],a=!0;for(;!this.eat(e);){if(a)a=!1;else if(this.expect(12),this.match(e)){i&&this.addTrailingCommaExtraToNode(i),this.next();break}r.push(this.parseExprListItem(t,s))}return r}parseExprListItem(e,t,s){let i;if(this.match(12))e||this.raise(d.UnexpectedToken,this.state.curPosition(),{unexpected:","}),i=null;else if(this.match(21)){let e=this.state.startLoc;i=this.parseParenItem(this.parseSpread(t),e)}else if(this.match(17)){this.expectPlugin("partialApplication"),s||this.raise(d.UnexpectedArgumentPlaceholder,this.state.startLoc);let e=this.startNode();this.next(),i=this.finishNode(e,"ArgumentPlaceholder")}else i=this.parseMaybeAssignAllowIn(t,this.parseParenItem);return i}parseIdentifier(e){let t=this.startNode(),s=this.parseIdentifierName(e);return this.createIdentifier(t,s)}createIdentifier(e,t){return e.name=t,e.loc.identifierName=t,this.finishNode(e,"Identifier")}parseIdentifierName(e){let t,{startLoc:s,type:i}=this.state;O(i)?t=this.state.value:this.unexpected();let r=i<=92;return e?r&&this.replaceToken(132):this.checkReservedWord(t,s,r,!1),this.next(),t}checkReservedWord(e,t,s,i){if(!(e.length>10)){if(et.has(e)){if(s&&X.has(e))return void this.raise(d.UnexpectedKeyword,t,{keyword:e});if((this.state.strict?i?ee:Z:Q)(e,this.inModule))return void this.raise(d.UnexpectedReservedWord,t,{reservedWord:e});if("yield"===e){if(this.prodParam.hasYield)return void this.raise(d.YieldBindingIdentifier,t)}else if("await"===e){if(this.prodParam.hasAwait)return void this.raise(d.AwaitBindingIdentifier,t);if(this.scope.inStaticBlock)return void this.raise(d.AwaitBindingIdentifierInStaticBlock,t);this.expressionScope.recordAsyncArrowParametersError(t)}else if("arguments"===e&&this.scope.inClassAndNotInNonArrowFunction)return void this.raise(d.ArgumentsInClass,t)}}}recordAwaitIfAllowed(){let e=this.prodParam.hasAwait||1&this.optionFlags&&!this.scope.inFunction;return e&&!this.scope.inFunction&&(this.state.hasTopLevelAwait=!0),e}parseAwait(e){let t=this.startNodeAt(e);return this.expressionScope.recordParameterInitializerError(d.AwaitExpressionFormalParameter,t),this.eat(55)&&this.raise(d.ObsoleteAwaitStar,t),this.scope.inFunction||1&this.optionFlags||(this.isAmbiguousPrefixOrIdentifier()?this.ambiguousScriptDifferentAst=!0:this.sawUnambiguousESM=!0),this.state.soloAwait||(t.argument=this.parseMaybeUnary(null,!0)),this.finishNode(t,"AwaitExpression")}isAmbiguousPrefixOrIdentifier(){if(this.hasPrecedingLineBreak())return!0;let{type:e}=this.state;return 53===e||10===e||0===e||U(e)||102===e&&!this.state.containsEsc||138===e||56===e||this.hasPlugin("v8intrinsic")&&54===e}parseYield(e){let t=this.startNodeAt(e);this.expressionScope.recordParameterInitializerError(d.YieldInParameter,t);let s=!1,i=null;if(!this.hasPrecedingLineBreak())switch(s=this.eat(55),this.state.type){case 13:case 140:case 8:case 11:case 3:case 9:case 14:case 12:if(!s)break;default:i=this.parseMaybeAssign()}return t.delegate=s,t.argument=i,this.finishNode(t,"YieldExpression")}parseImportCall(e){if(this.next(),e.source=this.parseMaybeAssignAllowIn(),e.options=null,this.eat(12)&&!this.match(11)&&(e.options=this.parseMaybeAssignAllowIn(),this.eat(12)&&!this.match(11))){do this.parseMaybeAssignAllowIn();while(this.eat(12)&&!this.match(11));this.raise(d.ImportCallArity,e)}return this.expect(11),this.finishNode(e,"ImportExpression")}checkPipelineAtInfixOperator(e,t){this.hasPlugin(["pipelineOperator",{proposal:"smart"}])&&"SequenceExpression"===e.type&&this.raise(d.PipelineHeadSequenceExpression,t)}parseSmartPipelineBodyInStyle(e,t){if(this.isSimpleReference(e)){let s=this.startNodeAt(t);return s.callee=e,this.finishNode(s,"PipelineBareFunction")}{let s=this.startNodeAt(t);return this.checkSmartPipeTopicBodyEarlyErrors(t),s.expression=e,this.finishNode(s,"PipelineTopicExpression")}}isSimpleReference(e){switch(e.type){case"MemberExpression":return!e.computed&&this.isSimpleReference(e.object);case"Identifier":return!0;default:return!1}}checkSmartPipeTopicBodyEarlyErrors(e){if(this.match(19))throw this.raise(d.PipelineBodyNoArrow,this.state.startLoc);this.topicReferenceWasUsedInCurrentContext()||this.raise(d.PipelineTopicUnused,e)}withTopicBindingContext(e){let t=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return e()}finally{this.state.topicContext=t}}withSmartMixTopicForbiddingContext(e){if(!this.hasPlugin(["pipelineOperator",{proposal:"smart"}]))return e();{let t=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return e()}finally{this.state.topicContext=t}}}withSoloAwaitPermittingContext(e){let t=this.state.soloAwait;this.state.soloAwait=!0;try{return e()}finally{this.state.soloAwait=t}}allowInAnd(e){let t=this.prodParam.currentFlags();if(8&~t){this.prodParam.enter(8|t);try{return e()}finally{this.prodParam.exit()}}return e()}disallowInAnd(e){let t=this.prodParam.currentFlags();if(8&t){this.prodParam.enter(-9&t);try{return e()}finally{this.prodParam.exit()}}return e()}registerTopicReference(){this.state.topicContext.maxTopicIndex=0}topicReferenceIsAllowedInCurrentContext(){return this.state.topicContext.maxNumOfResolvableTopics>=1}topicReferenceWasUsedInCurrentContext(){return null!=this.state.topicContext.maxTopicIndex&&this.state.topicContext.maxTopicIndex>=0}parseFSharpPipelineBody(e){let t=this.state.startLoc;this.state.potentialArrowAt=this.state.start;let s=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!0;let i=this.parseExprOp(this.parseMaybeUnaryOrPrivate(),t,e);return this.state.inFSharpPipelineDirectBody=s,i}parseModuleExpression(){this.expectPlugin("moduleBlocks");let e=this.startNode();this.next(),this.match(5)||this.unexpected(null,5);let t=this.startNodeAt(this.state.endLoc);this.next();let s=this.initializeScopes(!0);this.enterInitialScopes();try{e.body=this.parseProgram(t,8,"module")}finally{s()}return this.finishNode(e,"ModuleExpression")}parsePropertyNamePrefixOperator(e){}}let e7={kind:1},te={kind:2},tt=/[\uD800-\uDFFF]/u,ts=/in(?:stanceof)?/y;class ti extends e9{parseTopLevel(e,t){return e.program=this.parseProgram(t),e.comments=this.comments,256&this.optionFlags&&(e.tokens=function(e,t,s){for(let i=0;i<e.length;i++){let a=e[i],{type:n}=a;if("number"==typeof n){if(139===n){let{loc:t,start:s,value:n,end:o}=a,h=s+1,l=r(t.start,1);e.splice(i,1,new eI({type:S[27],value:"#",start:s,end:h,startLoc:t.start,endLoc:l}),new eI({type:S[132],value:n,start:h,end:o,startLoc:l,endLoc:t.end})),i++;continue}if(U(n)){let o,h,l,p,c,{loc:u,start:d,value:m,end:f}=a,y=d+1,x=r(u.start,1);o=new eI(96===t.charCodeAt(d-s)?{type:S[22],value:"`",start:d,end:y,startLoc:u.start,endLoc:x}:{type:S[8],value:"}",start:d,end:y,startLoc:u.start,endLoc:x}),24===n?(l=f-1,p=r(u.end,-1),h=null===m?null:m.slice(1,-1),c=new eI({type:S[22],value:"`",start:l,end:f,startLoc:p,endLoc:u.end})):(l=f-2,p=r(u.end,-2),h=null===m?null:m.slice(1,-2),c=new eI({type:S[23],value:"${",start:l,end:f,startLoc:p,endLoc:u.end})),e.splice(i,1,o,new eI({type:S[20],value:h,start:y,end:l,startLoc:x,endLoc:p}),c),i+=2;continue}a.type=S[n]}}return e}(this.tokens,this.input,this.startIndex)),this.finishNode(e,"File")}parseProgram(e,t=140,s=this.options.sourceType){let i;if(e.sourceType=s,e.interpreter=this.parseInterpreterDirective(),this.parseBlockBody(e,!0,!0,t),this.inModule){if(!(64&this.optionFlags)&&this.scope.undefinedExports.size>0)for(let[e,t]of Array.from(this.scope.undefinedExports))this.raise(d.ModuleExportUndefined,t,{localName:e});this.addExtra(e,"topLevelAwait",this.state.hasTopLevelAwait)}return 140===t?this.finishNode(e,"Program"):this.finishNodeAt(e,"Program",r(this.state.startLoc,-1))}stmtToDirective(e){e.type="Directive",e.value=e.expression,delete e.expression;let t=e.value,s=t.value,i=this.input.slice(this.offsetToSourcePos(t.start),this.offsetToSourcePos(t.end)),r=t.value=i.slice(1,-1);return this.addExtra(t,"raw",i),this.addExtra(t,"rawValue",r),this.addExtra(t,"expressionValue",s),t.type="DirectiveLiteral",e}parseInterpreterDirective(){if(!this.match(28))return null;let e=this.startNode();return e.value=this.state.value,this.next(),this.finishNode(e,"InterpreterDirective")}isLet(){return!!this.isContextual(100)&&this.hasFollowingBindingAtom()}chStartsBindingIdentifier(e,t){if(K(e)){if(ts.lastIndex=t,ts.test(this.input)){let e=this.codePointAtPos(ts.lastIndex);if(!J(e)&&92!==e)return!1}return!0}return 92===e||!1}chStartsBindingPattern(e){return 91===e||123===e}hasFollowingBindingAtom(){let e=this.nextTokenStart(),t=this.codePointAtPos(e);return this.chStartsBindingPattern(t)||this.chStartsBindingIdentifier(t,e)}hasInLineFollowingBindingIdentifierOrBrace(){let e=this.nextTokenInLineStart(),t=this.codePointAtPos(e);return 123===t||this.chStartsBindingIdentifier(t,e)}startsUsingForOf(){let{type:e,containsEsc:t}=this.lookahead();return(102!==e||!!t)&&(D(e)&&!this.hasFollowingLineBreak()?(this.expectPlugin("explicitResourceManagement"),!0):void 0)}startsAwaitUsing(){let e=this.nextTokenInLineStart();if(this.isUnparsedContextual(e,"using")){e=this.nextTokenInLineStartSince(e+5);let t=this.codePointAtPos(e);if(this.chStartsBindingIdentifier(t,e))return this.expectPlugin("explicitResourceManagement"),!0}return!1}parseModuleItem(){return this.parseStatementLike(15)}parseStatementListItem(){return this.parseStatementLike(6|(!this.options.annexB||this.state.strict?0:8))}parseStatementOrSloppyAnnexBFunctionDeclaration(e=!1){let t=0;return this.options.annexB&&!this.state.strict&&(t|=4,e&&(t|=8)),this.parseStatementLike(t)}parseStatement(){return this.parseStatementLike(0)}parseStatementLike(e){let t=null;return this.match(26)&&(t=this.parseDecorators(!0)),this.parseStatementContent(e,t)}parseStatementContent(e,t){let s=this.state.type,i=this.startNode(),r=!!(2&e),a=!!(4&e),n=1&e;switch(s){case 60:return this.parseBreakContinueStatement(i,!0);case 63:return this.parseBreakContinueStatement(i,!1);case 64:return this.parseDebuggerStatement(i);case 90:return this.parseDoWhileStatement(i);case 91:return this.parseForStatement(i);case 68:if(46===this.lookaheadCharCode())break;return a||this.raise(this.state.strict?d.StrictFunction:this.options.annexB?d.SloppyFunctionAnnexB:d.SloppyFunction,this.state.startLoc),this.parseFunctionStatement(i,!1,!r&&a);case 80:return r||this.unexpected(),this.parseClass(this.maybeTakeDecorators(t,i),!0);case 69:return this.parseIfStatement(i);case 70:return this.parseReturnStatement(i);case 71:return this.parseSwitchStatement(i);case 72:return this.parseThrowStatement(i);case 73:return this.parseTryStatement(i);case 96:if(!this.state.containsEsc&&this.startsAwaitUsing())return this.recordAwaitIfAllowed()?r||this.raise(d.UnexpectedLexicalDeclaration,i):this.raise(d.AwaitUsingNotInAsyncContext,i),this.next(),this.parseVarStatement(i,"await using");break;case 107:if(this.state.containsEsc||!this.hasInLineFollowingBindingIdentifierOrBrace())break;return this.expectPlugin("explicitResourceManagement"),!this.scope.inModule&&this.scope.inTopLevel?this.raise(d.UnexpectedUsingDeclaration,this.state.startLoc):r||this.raise(d.UnexpectedLexicalDeclaration,this.state.startLoc),this.parseVarStatement(i,"using");case 100:{if(this.state.containsEsc)break;let e=this.nextTokenStart(),t=this.codePointAtPos(e);if(91!==t&&(!r&&this.hasFollowingLineBreak()||!this.chStartsBindingIdentifier(t,e)&&123!==t))break}case 75:r||this.raise(d.UnexpectedLexicalDeclaration,this.state.startLoc);case 74:{let e=this.state.value;return this.parseVarStatement(i,e)}case 92:return this.parseWhileStatement(i);case 76:return this.parseWithStatement(i);case 5:return this.parseBlock();case 13:return this.parseEmptyStatement(i);case 83:{let e=this.lookaheadCharCode();if(40===e||46===e)break}case 82:{let e;return 8&this.optionFlags||n||this.raise(d.UnexpectedImportExport,this.state.startLoc),this.next(),e=83===s?this.parseImport(i):this.parseExport(i,t),this.assertModuleNodeAllowed(e),e}default:if(this.isAsyncFunction())return r||this.raise(d.AsyncFunctionInSingleStatementContext,this.state.startLoc),this.next(),this.parseFunctionStatement(i,!0,!r&&a)}let o=this.state.value,h=this.parseExpression();return D(s)&&"Identifier"===h.type&&this.eat(14)?this.parseLabeledStatement(i,o,h,e):this.parseExpressionStatement(i,h,t)}assertModuleNodeAllowed(e){8&this.optionFlags||this.inModule||this.raise(d.ImportOutsideModule,e)}decoratorsEnabledBeforeExport(){return!!this.hasPlugin("decorators-legacy")||this.hasPlugin("decorators")&&!1!==this.getPluginOption("decorators","decoratorsBeforeExport")}maybeTakeDecorators(e,t,s){if(e){var i;null!=(i=t.decorators)&&i.length?("boolean"!=typeof this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(d.DecoratorsBeforeAfterExport,t.decorators[0]),t.decorators.unshift(...e)):t.decorators=e,this.resetStartLocationFromNode(t,e[0]),s&&this.resetStartLocationFromNode(s,t)}return t}canHaveLeadingDecorator(){return this.match(80)}parseDecorators(e){let t=[];do t.push(this.parseDecorator());while(this.match(26));if(this.match(82))e||this.unexpected(),this.decoratorsEnabledBeforeExport()||this.raise(d.DecoratorExportClass,this.state.startLoc);else if(!this.canHaveLeadingDecorator())throw this.raise(d.UnexpectedLeadingDecorator,this.state.startLoc);return t}parseDecorator(){this.expectOnePlugin(["decorators","decorators-legacy"]);let e=this.startNode();if(this.next(),this.hasPlugin("decorators")){let t,s=this.state.startLoc;if(this.match(10)){let s=this.state.startLoc;this.next(),t=this.parseExpression(),this.expect(11),t=this.wrapParenthesis(s,t);let i=this.state.startLoc;e.expression=this.parseMaybeDecoratorArguments(t,s),!1===this.getPluginOption("decorators","allowCallParenthesized")&&e.expression!==t&&this.raise(d.DecoratorArgumentsOutsideParentheses,i)}else{for(t=this.parseIdentifier(!1);this.eat(16);){let e=this.startNodeAt(s);e.object=t,this.match(139)?(this.classScope.usePrivateName(this.state.value,this.state.startLoc),e.property=this.parsePrivateName()):e.property=this.parseIdentifier(!0),e.computed=!1,t=this.finishNode(e,"MemberExpression")}e.expression=this.parseMaybeDecoratorArguments(t,s)}}else e.expression=this.parseExprSubscripts();return this.finishNode(e,"Decorator")}parseMaybeDecoratorArguments(e,t){if(this.eat(10)){let s=this.startNodeAt(t);return s.callee=e,s.arguments=this.parseCallExpressionArguments(11),this.toReferencedList(s.arguments),this.finishNode(s,"CallExpression")}return e}parseBreakContinueStatement(e,t){return this.next(),this.isLineTerminator()?e.label=null:(e.label=this.parseIdentifier(),this.semicolon()),this.verifyBreakContinue(e,t),this.finishNode(e,t?"BreakStatement":"ContinueStatement")}verifyBreakContinue(e,t){let s;for(s=0;s<this.state.labels.length;++s){let i=this.state.labels[s];if((null==e.label||i.name===e.label.name)&&(null!=i.kind&&(t||1===i.kind)||e.label&&t))break}s===this.state.labels.length&&this.raise(d.IllegalBreakContinue,e,{type:t?"BreakStatement":"ContinueStatement"})}parseDebuggerStatement(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")}parseHeaderExpression(){this.expect(10);let e=this.parseExpression();return this.expect(11),e}parseDoWhileStatement(e){return this.next(),this.state.labels.push(e7),e.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.state.labels.pop(),this.expect(92),e.test=this.parseHeaderExpression(),this.eat(13),this.finishNode(e,"DoWhileStatement")}parseForStatement(e){this.next(),this.state.labels.push(e7);let t=null;if(this.isContextual(96)&&this.recordAwaitIfAllowed()&&(t=this.state.startLoc,this.next()),this.scope.enter(0),this.expect(10),this.match(13))return null!==t&&this.unexpected(t),this.parseFor(e,null);let s=this.isContextual(100);{let i=this.isContextual(96)&&this.startsAwaitUsing(),r=i||this.isContextual(107)&&this.startsUsingForOf(),a=s&&this.hasFollowingBindingAtom()||r;if(this.match(74)||this.match(75)||a){let s,a=this.startNode();i?(s="await using",this.recordAwaitIfAllowed()||this.raise(d.AwaitUsingNotInAsyncContext,this.state.startLoc),this.next()):s=this.state.value,this.next(),this.parseVar(a,!0,s);let n=this.finishNode(a,"VariableDeclaration"),o=this.match(58);return(o&&r&&this.raise(d.ForInUsing,n),(o||this.isContextual(102))&&1===n.declarations.length)?this.parseForIn(e,n,t):(null!==t&&this.unexpected(t),this.parseFor(e,n))}}let i=this.isContextual(95),r=new eF,a=this.parseExpression(!0,r),n=this.isContextual(102);return(n&&(s&&this.raise(d.ForOfLet,a),null===t&&i&&"Identifier"===a.type&&this.raise(d.ForOfAsync,a)),n||this.match(58))?(this.checkDestructuringPrivate(r),this.toAssignable(a,!0),this.checkLVal(a,{type:n?"ForOfStatement":"ForInStatement"}),this.parseForIn(e,a,t)):(this.checkExpressionErrors(r,!0),null!==t&&this.unexpected(t),this.parseFor(e,a))}parseFunctionStatement(e,t,s){return this.next(),this.parseFunction(e,1|2*!!s|8*!!t)}parseIfStatement(e){return this.next(),e.test=this.parseHeaderExpression(),e.consequent=this.parseStatementOrSloppyAnnexBFunctionDeclaration(),e.alternate=this.eat(66)?this.parseStatementOrSloppyAnnexBFunctionDeclaration():null,this.finishNode(e,"IfStatement")}parseReturnStatement(e){return this.prodParam.hasReturn||2&this.optionFlags||this.raise(d.IllegalReturn,this.state.startLoc),this.next(),this.isLineTerminator()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")}parseSwitchStatement(e){let t,s;this.next(),e.discriminant=this.parseHeaderExpression();let i=e.cases=[];for(this.expect(5),this.state.labels.push(te),this.scope.enter(0);!this.match(8);)if(this.match(61)||this.match(65)){let e=this.match(61);t&&this.finishNode(t,"SwitchCase"),i.push(t=this.startNode()),t.consequent=[],this.next(),e?t.test=this.parseExpression():(s&&this.raise(d.MultipleDefaultsInSwitch,this.state.lastTokStartLoc),s=!0,t.test=null),this.expect(14)}else t?t.consequent.push(this.parseStatementListItem()):this.unexpected();return this.scope.exit(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.state.labels.pop(),this.finishNode(e,"SwitchStatement")}parseThrowStatement(e){return this.next(),this.hasPrecedingLineBreak()&&this.raise(d.NewlineAfterThrow,this.state.lastTokEndLoc),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")}parseCatchClauseParam(){let e=this.parseBindingAtom();return this.scope.enter(this.options.annexB&&"Identifier"===e.type?8:0),this.checkLVal(e,{type:"CatchClause"},9),e}parseTryStatement(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.match(62)){let t=this.startNode();this.next(),this.match(10)?(this.expect(10),t.param=this.parseCatchClauseParam(),this.expect(11)):(t.param=null,this.scope.enter(0)),t.body=this.withSmartMixTopicForbiddingContext(()=>this.parseBlock(!1,!1)),this.scope.exit(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(67)?this.parseBlock():null,e.handler||e.finalizer||this.raise(d.NoCatchOrFinally,e),this.finishNode(e,"TryStatement")}parseVarStatement(e,t,s=!1){return this.next(),this.parseVar(e,!1,t,s),this.semicolon(),this.finishNode(e,"VariableDeclaration")}parseWhileStatement(e){return this.next(),e.test=this.parseHeaderExpression(),this.state.labels.push(e7),e.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.state.labels.pop(),this.finishNode(e,"WhileStatement")}parseWithStatement(e){return this.state.strict&&this.raise(d.StrictWith,this.state.startLoc),this.next(),e.object=this.parseHeaderExpression(),e.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.finishNode(e,"WithStatement")}parseEmptyStatement(e){return this.next(),this.finishNode(e,"EmptyStatement")}parseLabeledStatement(e,t,s,i){var r;for(let e of this.state.labels)e.name===t&&this.raise(d.LabelRedeclaration,s,{labelName:t});let a=(r=this.state.type)>=90&&r<=92?1:this.match(71)?2:null;for(let t=this.state.labels.length-1;t>=0;t--){let s=this.state.labels[t];if(s.statementStart===e.start)s.statementStart=this.sourceToOffsetPos(this.state.start),s.kind=a;else break}return this.state.labels.push({name:t,kind:a,statementStart:this.sourceToOffsetPos(this.state.start)}),e.body=8&i?this.parseStatementOrSloppyAnnexBFunctionDeclaration(!0):this.parseStatement(),this.state.labels.pop(),e.label=s,this.finishNode(e,"LabeledStatement")}parseExpressionStatement(e,t,s){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")}parseBlock(e=!1,t=!0,s){let i=this.startNode();return e&&this.state.strictErrors.clear(),this.expect(5),t&&this.scope.enter(0),this.parseBlockBody(i,e,!1,8,s),t&&this.scope.exit(),this.finishNode(i,"BlockStatement")}isValidDirective(e){return"ExpressionStatement"===e.type&&"StringLiteral"===e.expression.type&&!e.expression.extra.parenthesized}parseBlockBody(e,t,s,i,r){let a=e.body=[],n=e.directives=[];this.parseBlockOrModuleBlockBody(a,t?n:void 0,s,i,r)}parseBlockOrModuleBlockBody(e,t,s,i,r){let a=this.state.strict,n=!1,o=!1;for(;!this.match(i);){let i=s?this.parseModuleItem():this.parseStatementListItem();if(t&&!o){if(this.isValidDirective(i)){let e=this.stmtToDirective(i);t.push(e),n||"use strict"!==e.value.value||(n=!0,this.setStrict(!0));continue}o=!0,this.state.strictErrors.clear()}e.push(i)}null==r||r.call(this,n),a||this.setStrict(!1),this.next()}parseFor(e,t){return e.init=t,this.semicolon(!1),e.test=this.match(13)?null:this.parseExpression(),this.semicolon(!1),e.update=this.match(11)?null:this.parseExpression(),this.expect(11),e.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.scope.exit(),this.state.labels.pop(),this.finishNode(e,"ForStatement")}parseForIn(e,t,s){let i=this.match(58);return this.next(),i?null!==s&&this.unexpected(s):e.await=null!==s,"VariableDeclaration"!==t.type||null==t.declarations[0].init||i&&this.options.annexB&&!this.state.strict&&"var"===t.kind&&"Identifier"===t.declarations[0].id.type||this.raise(d.ForInOfLoopInitializer,t,{type:i?"ForInStatement":"ForOfStatement"}),"AssignmentPattern"===t.type&&this.raise(d.InvalidLhs,t,{ancestor:{type:"ForStatement"}}),e.left=t,e.right=i?this.parseExpression():this.parseMaybeAssignAllowIn(),this.expect(11),e.body=this.withSmartMixTopicForbiddingContext(()=>this.parseStatement()),this.scope.exit(),this.state.labels.pop(),this.finishNode(e,i?"ForInStatement":"ForOfStatement")}parseVar(e,t,s,i=!1){let r=e.declarations=[];for(e.kind=s;;){let e=this.startNode();if(this.parseVarId(e,s),e.init=this.eat(29)?t?this.parseMaybeAssignDisallowIn():this.parseMaybeAssignAllowIn():null,null===e.init&&!i&&("Identifier"===e.id.type||t&&(this.match(58)||this.isContextual(102))?"const"!==s&&"using"!==s&&"await using"!==s||this.match(58)||this.isContextual(102)||this.raise(d.DeclarationMissingInitializer,this.state.lastTokEndLoc,{kind:s}):this.raise(d.DeclarationMissingInitializer,this.state.lastTokEndLoc,{kind:"destructuring"})),r.push(this.finishNode(e,"VariableDeclarator")),!this.eat(12))break}return e}parseVarId(e,t){let s=this.parseBindingAtom();("using"===t||"await using"===t)&&("ArrayPattern"===s.type||"ObjectPattern"===s.type)&&this.raise(d.UsingDeclarationHasBindingPattern,s.loc.start),this.checkLVal(s,{type:"VariableDeclarator"},"var"===t?5:8201),e.id=s}parseAsyncFunctionExpression(e){return this.parseFunction(e,8)}parseFunction(e,t=0){let s=2&t,i=!!(1&t),r=!!(8&t);this.initFunction(e,r),this.match(55)&&(s&&this.raise(d.GeneratorInSingleStatementContext,this.state.startLoc),this.next(),e.generator=!0),i&&(e.id=this.parseFunctionId(i&&!(4&t)));let a=this.state.maybeInArrowParameters;return this.state.maybeInArrowParameters=!1,this.scope.enter(2),this.prodParam.enter(eD(r,e.generator)),i||(e.id=this.parseFunctionId()),this.parseFunctionParams(e,!1),this.withSmartMixTopicForbiddingContext(()=>{this.parseFunctionBodyAndFinish(e,i?"FunctionDeclaration":"FunctionExpression")}),this.prodParam.exit(),this.scope.exit(),i&&!s&&this.registerFunctionStatementId(e),this.state.maybeInArrowParameters=a,e}parseFunctionId(e){return e||D(this.state.type)?this.parseIdentifier():null}parseFunctionParams(e,t){this.expect(10),this.expressionScope.enter(new ev(3)),e.params=this.parseBindingList(11,41,2|4*!!t),this.expressionScope.exit()}registerFunctionStatementId(e){e.id&&this.scope.declareName(e.id.name,!this.options.annexB||this.state.strict||e.generator||e.async?this.scope.treatFunctionsAsVar?5:8201:17,e.id.loc.start)}parseClass(e,t,s){this.next();let i=this.state.strict;return this.state.strict=!0,this.parseClassId(e,t,s),this.parseClassSuper(e),e.body=this.parseClassBody(!!e.superClass,i),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")}isClassProperty(){return this.match(29)||this.match(13)||this.match(8)}isClassMethod(){return this.match(10)}nameIsConstructor(e){return"Identifier"===e.type&&"constructor"===e.name||"StringLiteral"===e.type&&"constructor"===e.value}isNonstaticConstructor(e){return!e.computed&&!e.static&&this.nameIsConstructor(e.key)}parseClassBody(e,t){this.classScope.enter();let s={hadConstructor:!1,hadSuperClass:e},i=[],r=this.startNode();if(r.body=[],this.expect(5),this.withSmartMixTopicForbiddingContext(()=>{for(;!this.match(8);){if(this.eat(13)){if(i.length>0)throw this.raise(d.DecoratorSemicolon,this.state.lastTokEndLoc);continue}if(this.match(26)){i.push(this.parseDecorator());continue}let e=this.startNode();i.length&&(e.decorators=i,this.resetStartLocationFromNode(e,i[0]),i=[]),this.parseClassMember(r,e,s),"constructor"===e.kind&&e.decorators&&e.decorators.length>0&&this.raise(d.DecoratorConstructor,e)}}),this.state.strict=t,this.next(),i.length)throw this.raise(d.TrailingDecorator,this.state.startLoc);return this.classScope.exit(),this.finishNode(r,"ClassBody")}parseClassMemberFromModifier(e,t){let s=this.parseIdentifier(!0);return this.isClassMethod()?(t.kind="method",t.computed=!1,t.key=s,t.static=!1,this.pushClassMethod(e,t,!1,!1,!1,!1),!0):this.isClassProperty()?(t.computed=!1,t.key=s,t.static=!1,e.body.push(this.parseClassProperty(t)),!0):(this.resetPreviousNodeTrailingComments(s),!1)}parseClassMember(e,t,s){let i=this.isContextual(106);if(i){if(this.parseClassMemberFromModifier(e,t))return;if(this.eat(5))return void this.parseClassStaticBlock(e,t)}this.parseClassMemberWithIsStatic(e,t,s,i)}parseClassMemberWithIsStatic(e,t,s,i){if(t.static=i,this.parsePropertyNamePrefixOperator(t),this.eat(55)){t.kind="method";let s=this.match(139);return(this.parseClassElementName(t),s)?void this.pushClassPrivateMethod(e,t,!0,!1):(this.isNonstaticConstructor(t)&&this.raise(d.ConstructorIsGenerator,t.key),void this.pushClassMethod(e,t,!0,!1,!1,!1))}let r=!this.state.containsEsc&&D(this.state.type),a=this.parseClassElementName(t),n=r?a.name:null,o=this.isPrivateName(a),h=this.state.startLoc;if(this.parsePostMemberNameModifiers(t),this.isClassMethod()){if(t.kind="method",o)return void this.pushClassPrivateMethod(e,t,!1,!1);let i=this.isNonstaticConstructor(t),r=!1;i&&(t.kind="constructor",s.hadConstructor&&!this.hasPlugin("typescript")&&this.raise(d.DuplicateConstructor,a),i&&this.hasPlugin("typescript")&&t.override&&this.raise(d.OverrideOnConstructor,a),s.hadConstructor=!0,r=s.hadSuperClass),this.pushClassMethod(e,t,!1,!1,i,r)}else if(this.isClassProperty())o?this.pushClassPrivateProperty(e,t):this.pushClassProperty(e,t);else if("async"!==n||this.isLineTerminator())if("get"!==n&&"set"!==n||this.match(55)&&this.isLineTerminator())if("accessor"!==n||this.isLineTerminator())this.isLineTerminator()?o?this.pushClassPrivateProperty(e,t):this.pushClassProperty(e,t):this.unexpected();else{this.expectPlugin("decoratorAutoAccessors"),this.resetPreviousNodeTrailingComments(a);let s=this.match(139);this.parseClassElementName(t),this.pushClassAccessorProperty(e,t,s)}else{this.resetPreviousNodeTrailingComments(a),t.kind=n;let s=this.match(139);this.parseClassElementName(t),s?this.pushClassPrivateMethod(e,t,!1,!1):(this.isNonstaticConstructor(t)&&this.raise(d.ConstructorIsAccessor,t.key),this.pushClassMethod(e,t,!1,!1,!1,!1)),this.checkGetterSetterParams(t)}else{this.resetPreviousNodeTrailingComments(a);let s=this.eat(55);t.optional&&this.unexpected(h),t.kind="method";let i=this.match(139);this.parseClassElementName(t),this.parsePostMemberNameModifiers(t),i?this.pushClassPrivateMethod(e,t,s,!0):(this.isNonstaticConstructor(t)&&this.raise(d.ConstructorIsAsync,t.key),this.pushClassMethod(e,t,s,!0,!1,!1))}}parseClassElementName(e){let{type:t,value:s}=this.state;if((132===t||134===t)&&e.static&&"prototype"===s&&this.raise(d.StaticPrototype,this.state.startLoc),139===t){"constructor"===s&&this.raise(d.ConstructorClassPrivateField,this.state.startLoc);let t=this.parsePrivateName();return e.key=t,t}return this.parsePropertyName(e),e.key}parseClassStaticBlock(e,t){var s;this.scope.enter(208);let i=this.state.labels;this.state.labels=[],this.prodParam.enter(0);let r=t.body=[];this.parseBlockOrModuleBlockBody(r,void 0,!1,8),this.prodParam.exit(),this.scope.exit(),this.state.labels=i,e.body.push(this.finishNode(t,"StaticBlock")),null!=(s=t.decorators)&&s.length&&this.raise(d.DecoratorStaticBlock,t)}pushClassProperty(e,t){!t.computed&&this.nameIsConstructor(t.key)&&this.raise(d.ConstructorClassField,t.key),e.body.push(this.parseClassProperty(t))}pushClassPrivateProperty(e,t){let s=this.parseClassPrivateProperty(t);e.body.push(s),this.classScope.declarePrivateName(this.getPrivateNameSV(s.key),0,s.key.loc.start)}pushClassAccessorProperty(e,t,s){!s&&!t.computed&&this.nameIsConstructor(t.key)&&this.raise(d.ConstructorClassField,t.key);let i=this.parseClassAccessorProperty(t);e.body.push(i),s&&this.classScope.declarePrivateName(this.getPrivateNameSV(i.key),0,i.key.loc.start)}pushClassMethod(e,t,s,i,r,a){e.body.push(this.parseMethod(t,s,i,r,a,"ClassMethod",!0))}pushClassPrivateMethod(e,t,s,i){let r=this.parseMethod(t,s,i,!1,!1,"ClassPrivateMethod",!0);e.body.push(r);let a="get"===r.kind?r.static?6:2:"set"===r.kind?r.static?5:1:0;this.declareClassPrivateMethodInScope(r,a)}declareClassPrivateMethodInScope(e,t){this.classScope.declarePrivateName(this.getPrivateNameSV(e.key),t,e.key.loc.start)}parsePostMemberNameModifiers(e){}parseClassPrivateProperty(e){return this.parseInitializer(e),this.semicolon(),this.finishNode(e,"ClassPrivateProperty")}parseClassProperty(e){return this.parseInitializer(e),this.semicolon(),this.finishNode(e,"ClassProperty")}parseClassAccessorProperty(e){return this.parseInitializer(e),this.semicolon(),this.finishNode(e,"ClassAccessorProperty")}parseInitializer(e){this.scope.enter(80),this.expressionScope.enter(new ev),this.prodParam.enter(0),e.value=this.eat(29)?this.parseMaybeAssignAllowIn():null,this.expressionScope.exit(),this.prodParam.exit(),this.scope.exit()}parseClassId(e,t,s,i=8331){if(D(this.state.type))e.id=this.parseIdentifier(),t&&this.declareNameFromIdentifier(e.id,i);else if(s||!t)e.id=null;else throw this.raise(d.MissingClassName,this.state.startLoc)}parseClassSuper(e){e.superClass=this.eat(81)?this.parseExprSubscripts():null}parseExport(e,t){let s,i=this.parseMaybeImportPhase(e,!0),r=this.maybeParseExportDefaultSpecifier(e,i),a=!r||this.eat(12),n=a&&this.eatExportStar(e),o=n&&this.maybeParseExportNamespaceSpecifier(e),h=a&&(!o||this.eat(12)),l=r||n;if(n&&!o){if(r&&this.unexpected(),t)throw this.raise(d.UnsupportedDecoratorExport,e);return this.parseExportFrom(e,!0),this.sawUnambiguousESM=!0,this.finishNode(e,"ExportAllDeclaration")}let p=this.maybeParseExportNamedSpecifiers(e);if(r&&a&&!n&&!p&&this.unexpected(null,5),o&&h&&this.unexpected(null,98),l||p){if(s=!1,t)throw this.raise(d.UnsupportedDecoratorExport,e);this.parseExportFrom(e,l)}else s=this.maybeParseExportDeclaration(e);if(l||p||s){var c;if(this.checkExport(e,!0,!1,!!e.source),(null==(c=e.declaration)?void 0:c.type)==="ClassDeclaration")this.maybeTakeDecorators(t,e.declaration,e);else if(t)throw this.raise(d.UnsupportedDecoratorExport,e);return this.sawUnambiguousESM=!0,this.finishNode(e,"ExportNamedDeclaration")}if(this.eat(65)){let s=this.parseExportDefaultExpression();if(e.declaration=s,"ClassDeclaration"===s.type)this.maybeTakeDecorators(t,s,e);else if(t)throw this.raise(d.UnsupportedDecoratorExport,e);return this.checkExport(e,!0,!0),this.sawUnambiguousESM=!0,this.finishNode(e,"ExportDefaultDeclaration")}this.unexpected(null,5)}eatExportStar(e){return this.eat(55)}maybeParseExportDefaultSpecifier(e,t){if(t||this.isExportDefaultSpecifier()){this.expectPlugin("exportDefaultFrom",null==t?void 0:t.loc.start);let s=t||this.parseIdentifier(!0),i=this.startNodeAtNode(s);return i.exported=s,e.specifiers=[this.finishNode(i,"ExportDefaultSpecifier")],!0}return!1}maybeParseExportNamespaceSpecifier(e){if(this.isContextual(93)){null!=e.specifiers||(e.specifiers=[]);let t=this.startNodeAt(this.state.lastTokStartLoc);return this.next(),t.exported=this.parseModuleExportName(),e.specifiers.push(this.finishNode(t,"ExportNamespaceSpecifier")),!0}return!1}maybeParseExportNamedSpecifiers(e){if(this.match(5)){e.specifiers||(e.specifiers=[]);let t="type"===e.exportKind;return e.specifiers.push(...this.parseExportSpecifiers(t)),e.source=null,this.hasPlugin("importAssertions")?e.assertions=[]:e.attributes=[],e.declaration=null,!0}return!1}maybeParseExportDeclaration(e){return!!this.shouldParseExportDeclaration()&&(e.specifiers=[],e.source=null,this.hasPlugin("importAssertions")?e.assertions=[]:e.attributes=[],e.declaration=this.parseExportDeclaration(e),!0)}isAsyncFunction(){if(!this.isContextual(95))return!1;let e=this.nextTokenInLineStart();return this.isUnparsedContextual(e,"function")}parseExportDefaultExpression(){let e=this.startNode();if(this.match(68))return this.next(),this.parseFunction(e,5);if(this.isAsyncFunction())return this.next(),this.next(),this.parseFunction(e,13);if(this.match(80))return this.parseClass(e,!0,!0);if(this.match(26))return this.hasPlugin("decorators")&&!0===this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(d.DecoratorBeforeExport,this.state.startLoc),this.parseClass(this.maybeTakeDecorators(this.parseDecorators(!1),this.startNode()),!0,!0);if(this.match(75)||this.match(74)||this.isLet())throw this.raise(d.UnsupportedDefaultExport,this.state.startLoc);let t=this.parseMaybeAssignAllowIn();return this.semicolon(),t}parseExportDeclaration(e){return this.match(80)?this.parseClass(this.startNode(),!0,!1):this.parseStatementListItem()}isExportDefaultSpecifier(){let{type:e}=this.state;if(D(e)){if(95===e&&!this.state.containsEsc||100===e)return!1;if((130===e||129===e)&&!this.state.containsEsc){let{type:e}=this.lookahead();if(D(e)&&98!==e||5===e)return this.expectOnePlugin(["flow","typescript"]),!1}}else if(!this.match(65))return!1;let t=this.nextTokenStart(),s=this.isUnparsedContextual(t,"from");if(44===this.input.charCodeAt(t)||D(this.state.type)&&s)return!0;if(this.match(65)&&s){let e=this.input.charCodeAt(this.nextTokenStartSince(t+4));return 34===e||39===e}return!1}parseExportFrom(e,t){this.eatContextual(98)?(e.source=this.parseImportSource(),this.checkExport(e),this.maybeParseImportAttributes(e),this.checkJSONModuleImport(e)):t&&this.unexpected(),this.semicolon()}shouldParseExportDeclaration(){let{type:e}=this.state;return 26===e&&(this.expectOnePlugin(["decorators","decorators-legacy"]),this.hasPlugin("decorators"))?(!0===this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(d.DecoratorBeforeExport,this.state.startLoc),!0):this.isContextual(107)||this.isContextual(96)&&this.startsAwaitUsing()?(this.raise(d.UsingDeclarationExport,this.state.startLoc),!0):74===e||75===e||68===e||80===e||this.isLet()||this.isAsyncFunction()}checkExport(e,t,s,i){if(t){var r,a;if(s){if(this.checkDuplicateExports(e,"default"),this.hasPlugin("exportDefaultFrom")){let t=e.declaration;"Identifier"!==t.type||"from"!==t.name||t.end-t.start!=4||null!=(a=t.extra)&&a.parenthesized||this.raise(d.ExportDefaultFromAsIdentifier,t)}}else if(null!=(r=e.specifiers)&&r.length)for(let t of e.specifiers){let{exported:e}=t,s="Identifier"===e.type?e.name:e.value;if(this.checkDuplicateExports(t,s),!i&&t.local){let{local:e}=t;"Identifier"!==e.type?this.raise(d.ExportBindingIsString,t,{localName:e.value,exportName:s}):(this.checkReservedWord(e.name,e.loc.start,!0,!1),this.scope.checkLocalExport(e))}}else if(e.declaration){let t=e.declaration;if("FunctionDeclaration"===t.type||"ClassDeclaration"===t.type){let{id:s}=t;if(!s)throw Error("Assertion failure");this.checkDuplicateExports(e,s.name)}else if("VariableDeclaration"===t.type)for(let e of t.declarations)this.checkDeclaration(e.id)}}}checkDeclaration(e){if("Identifier"===e.type)this.checkDuplicateExports(e,e.name);else if("ObjectPattern"===e.type)for(let t of e.properties)this.checkDeclaration(t);else if("ArrayPattern"===e.type)for(let t of e.elements)t&&this.checkDeclaration(t);else"ObjectProperty"===e.type?this.checkDeclaration(e.value):"RestElement"===e.type?this.checkDeclaration(e.argument):"AssignmentPattern"===e.type&&this.checkDeclaration(e.left)}checkDuplicateExports(e,t){this.exportedIdentifiers.has(t)&&("default"===t?this.raise(d.DuplicateDefaultExport,e):this.raise(d.DuplicateExport,e,{exportName:t})),this.exportedIdentifiers.add(t)}parseExportSpecifiers(e){let t=[],s=!0;for(this.expect(5);!this.eat(8);){if(s)s=!1;else if(this.expect(12),this.eat(8))break;let i=this.isContextual(130),r=this.match(134),a=this.startNode();a.local=this.parseModuleExportName(),t.push(this.parseExportSpecifier(a,r,e,i))}return t}parseExportSpecifier(e,t,s,i){return this.eatContextual(93)?e.exported=this.parseModuleExportName():t?e.exported=function(e){let{type:t,start:s,end:i,loc:r,range:a,extra:n}=e;if("Placeholder"===t)return eU(e);let o=Object.create(eR);return o.type=t,o.start=s,o.end=i,o.loc=r,o.range=a,void 0!==e.raw?o.raw=e.raw:o.extra=n,o.value=e.value,o}(e.local):e.exported||(e.exported=eU(e.local)),this.finishNode(e,"ExportSpecifier")}parseModuleExportName(){if(this.match(134)){let e=this.parseStringLiteral(this.state.value),t=tt.exec(e.value);return t&&this.raise(d.ModuleExportNameHasLoneSurrogate,e,{surrogateCharCode:t[0].charCodeAt(0)}),e}return this.parseIdentifier(!0)}isJSONModuleImport(e){return null!=e.assertions&&e.assertions.some(({key:e,value:t})=>"json"===t.value&&("Identifier"===e.type?"type"===e.name:"type"===e.value))}checkImportReflection(e){let{specifiers:t}=e,s=1===t.length?t[0].type:null;if("source"===e.phase)"ImportDefaultSpecifier"!==s&&this.raise(d.SourcePhaseImportRequiresDefault,t[0].loc.start);else if("defer"===e.phase)"ImportNamespaceSpecifier"!==s&&this.raise(d.DeferImportRequiresNamespace,t[0].loc.start);else if(e.module){var i;"ImportDefaultSpecifier"!==s&&this.raise(d.ImportReflectionNotBinding,t[0].loc.start),(null==(i=e.assertions)?void 0:i.length)>0&&this.raise(d.ImportReflectionHasAssertion,t[0].loc.start)}}checkJSONModuleImport(e){if(this.isJSONModuleImport(e)&&"ExportAllDeclaration"!==e.type){let{specifiers:t}=e;if(null!=t){let e=t.find(e=>{let t;if("ExportSpecifier"===e.type?t=e.local:"ImportSpecifier"===e.type&&(t=e.imported),void 0!==t)return"Identifier"===t.type?"default"!==t.name:"default"!==t.value});void 0!==e&&this.raise(d.ImportJSONBindingNotDefault,e.loc.start)}}}isPotentialImportPhase(e){return!e&&(this.isContextual(105)||this.isContextual(97)||this.isContextual(127))}applyImportPhase(e,t,s,i){!t&&("module"===s?(this.expectPlugin("importReflection",i),e.module=!0):this.hasPlugin("importReflection")&&(e.module=!1),"source"===s?(this.expectPlugin("sourcePhaseImports",i),e.phase="source"):"defer"===s?(this.expectPlugin("deferredImportEvaluation",i),e.phase="defer"):this.hasPlugin("sourcePhaseImports")&&(e.phase=null))}parseMaybeImportPhase(e,t){if(!this.isPotentialImportPhase(t))return this.applyImportPhase(e,t,null),null;let s=this.parseIdentifier(!0),{type:i}=this.state;return(O(i)?98!==i||102===this.lookaheadCharCode():12!==i)?(this.resetPreviousIdentifierLeadingComments(s),this.applyImportPhase(e,t,s.name,s.loc.start),null):(this.applyImportPhase(e,t,null),s)}isPrecedingIdImportPhase(e){let{type:t}=this.state;return D(t)?98!==t||102===this.lookaheadCharCode():12!==t}parseImport(e){return this.match(134)?this.parseImportSourceAndAttributes(e):this.parseImportSpecifiersAndAfter(e,this.parseMaybeImportPhase(e,!1))}parseImportSpecifiersAndAfter(e,t){e.specifiers=[];let s=!this.maybeParseDefaultImportSpecifier(e,t)||this.eat(12),i=s&&this.maybeParseStarImportSpecifier(e);return s&&!i&&this.parseNamedImportSpecifiers(e),this.expectContextual(98),this.parseImportSourceAndAttributes(e)}parseImportSourceAndAttributes(e){return null!=e.specifiers||(e.specifiers=[]),e.source=this.parseImportSource(),this.maybeParseImportAttributes(e),this.checkImportReflection(e),this.checkJSONModuleImport(e),this.semicolon(),this.sawUnambiguousESM=!0,this.finishNode(e,"ImportDeclaration")}parseImportSource(){return this.match(134)||this.unexpected(),this.parseExprAtom()}parseImportSpecifierLocal(e,t,s){t.local=this.parseIdentifier(),e.specifiers.push(this.finishImportSpecifier(t,s))}finishImportSpecifier(e,t,s=8201){return this.checkLVal(e.local,{type:t},s),this.finishNode(e,t)}parseImportAttributes(){this.expect(5);let e=[],t=new Set;do{if(this.match(8))break;let s=this.startNode(),i=this.state.value;if(t.has(i)&&this.raise(d.ModuleAttributesWithDuplicateKeys,this.state.startLoc,{key:i}),t.add(i),this.match(134)?s.key=this.parseStringLiteral(i):s.key=this.parseIdentifier(!0),this.expect(14),!this.match(134))throw this.raise(d.ModuleAttributeInvalidValue,this.state.startLoc);s.value=this.parseStringLiteral(this.state.value),e.push(this.finishNode(s,"ImportAttribute"))}while(this.eat(12));return this.expect(8),e}parseModuleAttributes(){let e=[],t=new Set;do{let s=this.startNode();if(s.key=this.parseIdentifier(!0),"type"!==s.key.name&&this.raise(d.ModuleAttributeDifferentFromType,s.key),t.has(s.key.name)&&this.raise(d.ModuleAttributesWithDuplicateKeys,s.key,{key:s.key.name}),t.add(s.key.name),this.expect(14),!this.match(134))throw this.raise(d.ModuleAttributeInvalidValue,this.state.startLoc);s.value=this.parseStringLiteral(this.state.value),e.push(this.finishNode(s,"ImportAttribute"))}while(this.eat(12));return e}maybeParseImportAttributes(e){let t;var s=!1;if(this.match(76)){if(this.hasPrecedingLineBreak()&&40===this.lookaheadCharCode())return;this.next(),this.hasPlugin("moduleAttributes")?(t=this.parseModuleAttributes(),this.addExtra(e,"deprecatedWithLegacySyntax",!0)):t=this.parseImportAttributes(),s=!0}else this.isContextual(94)&&!this.hasPrecedingLineBreak()?(this.hasPlugin("deprecatedImportAssert")||this.hasPlugin("importAssertions")||this.raise(d.ImportAttributesUseAssert,this.state.startLoc),this.hasPlugin("importAssertions")||this.addExtra(e,"deprecatedAssertSyntax",!0),this.next(),t=this.parseImportAttributes()):t=[];!s&&this.hasPlugin("importAssertions")?e.assertions=t:e.attributes=t}maybeParseDefaultImportSpecifier(e,t){if(t){let s=this.startNodeAtNode(t);return s.local=t,e.specifiers.push(this.finishImportSpecifier(s,"ImportDefaultSpecifier")),!0}return!!O(this.state.type)&&(this.parseImportSpecifierLocal(e,this.startNode(),"ImportDefaultSpecifier"),!0)}maybeParseStarImportSpecifier(e){if(this.match(55)){let t=this.startNode();return this.next(),this.expectContextual(93),this.parseImportSpecifierLocal(e,t,"ImportNamespaceSpecifier"),!0}return!1}parseNamedImportSpecifiers(e){let t=!0;for(this.expect(5);!this.eat(8);){if(t)t=!1;else{if(this.eat(14))throw this.raise(d.DestructureNamedImport,this.state.startLoc);if(this.expect(12),this.eat(8))break}let s=this.startNode(),i=this.match(134),r=this.isContextual(130);s.imported=this.parseModuleExportName();let a=this.parseImportSpecifier(s,i,"type"===e.importKind||"typeof"===e.importKind,r,void 0);e.specifiers.push(a)}}parseImportSpecifier(e,t,s,i,r){if(this.eatContextual(93))e.local=this.parseIdentifier();else{let{imported:s}=e;if(t)throw this.raise(d.ImportBindingIsString,e,{importName:s.value});this.checkReservedWord(s.name,e.loc.start,!0,!0),e.local||(e.local=eU(s))}return this.finishImportSpecifier(e,"ImportSpecifier",r)}isThisParam(e){return"Identifier"===e.type&&"this"===e.name}}class tr extends ti{constructor(e,t,s){super(e=function(e){let t={sourceType:"script",sourceFilename:void 0,startIndex:0,startColumn:0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowNewTargetOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,allowYieldOutsideFunction:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createImportExpressions:!1,createParenthesizedExpressions:!1,errorRecovery:!1,attachComment:!0,annexB:!0};if(null==e)return t;if(null!=e.annexB&&!1!==e.annexB)throw Error("The `annexB` option can only be set to `false`.");for(let s of Object.keys(t))null!=e[s]&&(t[s]=e[s]);if(1===t.startLine)null==e.startIndex&&t.startColumn>0?t.startIndex=t.startColumn:null==e.startColumn&&t.startIndex>0&&(t.startColumn=t.startIndex);else if((null==e.startColumn||null==e.startIndex)&&null!=e.startIndex)throw Error("With a `startLine > 1` you must also specify `startIndex` and `startColumn`.");return t}(e),t),this.options=e,this.initializeScopes(),this.plugins=s,this.filename=e.sourceFilename,this.startIndex=e.startIndex;let i=0;e.allowAwaitOutsideFunction&&(i|=1),e.allowReturnOutsideFunction&&(i|=2),e.allowImportExportEverywhere&&(i|=8),e.allowSuperOutsideMethod&&(i|=16),e.allowUndeclaredExports&&(i|=64),e.allowNewTargetOutsideFunction&&(i|=4),e.allowYieldOutsideFunction&&(i|=32),e.ranges&&(i|=128),e.tokens&&(i|=256),e.createImportExpressions&&(i|=512),e.createParenthesizedExpressions&&(i|=1024),e.errorRecovery&&(i|=2048),e.attachComment&&(i|=4096),e.annexB&&(i|=8192),this.optionFlags=i}getScopeHandler(){return ei}parse(){this.enterInitialScopes();let e=this.startNode(),t=this.startNode();return this.nextToken(),e.errors=null,this.parseTopLevel(e,t),e.errors=this.state.errors,e.comments.length=this.state.commentsLen,e}}let ta=function(e){let t={};for(let s of Object.keys(e))t[s]=S[e[s]];return t}(M);function tn(e,t){let s=tr,i=new Map;if(null!=e&&e.plugins){for(let t of e.plugins){let e,s;"string"==typeof t?e=t:[e,s]=t,i.has(e)||i.set(e,s||{})}!function(e){if(e.has("decorators")){if(e.has("decorators-legacy"))throw Error("Cannot use the decorators and decorators-legacy plugin together");let t=e.get("decorators").decoratorsBeforeExport;if(null!=t&&"boolean"!=typeof t)throw Error("'decoratorsBeforeExport' must be a boolean, if specified.");let s=e.get("decorators").allowCallParenthesized;if(null!=s&&"boolean"!=typeof s)throw Error("'allowCallParenthesized' must be a boolean.")}if(e.has("flow")&&e.has("typescript"))throw Error("Cannot combine flow and typescript plugins.");if(e.has("placeholders")&&e.has("v8intrinsic"))throw Error("Cannot combine placeholders and v8intrinsic plugins.");if(e.has("pipelineOperator")){var t;let s=e.get("pipelineOperator").proposal;if(!e3.includes(s)){let e=e3.map(e=>`"${e}"`).join(", ");throw Error(`"pipelineOperator" requires "proposal" option whose value must be one of: ${e}.`)}let i=(null==(t=e.get("recordAndTuple"))?void 0:t.syntaxType)==="hash";if("hack"===s){if(e.has("placeholders"))throw Error("Cannot combine placeholders plugin and Hack-style pipes.");if(e.has("v8intrinsic"))throw Error("Cannot combine v8intrinsic plugin and Hack-style pipes.");let t=e.get("pipelineOperator").topicToken;if(!e5.includes(t)){let e=e5.map(e=>`"${e}"`).join(", ");throw Error(`"pipelineOperator" in "proposal": "hack" mode also requires a "topicToken" option whose value must be one of: ${e}.`)}if("#"===t&&i)throw Error(`Plugin conflict between \`["pipelineOperator", { proposal: "hack", topicToken: "#" }]\` and \`${JSON.stringify(["recordAndTuple",e.get("recordAndTuple")])}\`.`)}else if("smart"===s&&i)throw Error(`Plugin conflict between \`["pipelineOperator", { proposal: "smart" }]\` and \`${JSON.stringify(["recordAndTuple",e.get("recordAndTuple")])}\`.`)}if(e.has("moduleAttributes")){if(e.has("deprecatedImportAssert")||e.has("importAssertions"))throw Error("Cannot combine importAssertions, deprecatedImportAssert and moduleAttributes plugins.");if("may-2020"!==e.get("moduleAttributes").version)throw Error("The 'moduleAttributes' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is 'may-2020'.")}if(e.has("importAssertions")&&e.has("deprecatedImportAssert"))throw Error("Cannot combine importAssertions and deprecatedImportAssert plugins.");if(!e.has("deprecatedImportAssert")&&e.has("importAttributes")&&e.get("importAttributes").deprecatedAssertSyntax&&e.set("deprecatedImportAssert",{}),e.has("recordAndTuple")){let t=e.get("recordAndTuple").syntaxType;if(null!=t){let e=["hash","bar"];if(!e.includes(t))throw Error("The 'syntaxType' option of the 'recordAndTuple' plugin must be one of: "+e.map(e=>`'${e}'`).join(", "))}}if(e.has("asyncDoExpressions")&&!e.has("doExpressions")){let e=Error("'asyncDoExpressions' requires 'doExpressions', please add 'doExpressions' to parser plugins.");throw e.missingPlugins="doExpressions",e}if(e.has("optionalChainingAssign")&&"2023-07"!==e.get("optionalChainingAssign").version)throw Error("The 'optionalChainingAssign' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is '2023-07'.")}(i),s=function(e){let t=[];for(let s of e6)e.has(s)&&t.push(s);let s=t.join("|"),i=to.get(s);if(!i){for(let e of(i=tr,t))i=e8[e](i);to.set(s,i)}return i}(i)}return new s(e,t,i)}let to=new Map;t.parse=function(e,t){var s;if((null==(s=t)?void 0:s.sourceType)!=="unambiguous")return tn(t,e).parse();t=Object.assign({},t);try{t.sourceType="module";let s=tn(t,e),i=s.parse();if(s.sawUnambiguousESM)return i;if(s.ambiguousScriptDifferentAst)try{return t.sourceType="script",tn(t,e).parse()}catch(e){}else i.program.sourceType="script";return i}catch(s){try{return t.sourceType="script",tn(t,e).parse()}catch(e){}throw s}},t.parseExpression=function(e,t){let s=tn(t,e);return s.options.strictMode&&(s.state.strict=!0),s.getExpression()},t.tokTypes=ta}}]);