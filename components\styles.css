.chat-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.message-container {
  margin-bottom: 20px;
}

.user-message {
  background-color: #f0f0f0;
  padding: 10px 15px;
  border-radius: 10px;
  margin-bottom: 10px;
}

.assistant-message {
  background-color: #f9f9f9;
  padding: 10px 15px;
  border-radius: 10px;
  margin-bottom: 10px;
}

.citation {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
  padding: 8px 12px;
  margin: 10px 0;
  font-size: 0.9em;
}

.citation-number {
  color: #1890ff;
  cursor: pointer;
  font-weight: bold;
}

.citation-content {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  padding: 10px;
  margin-top: 5px;
  background-color: #fafafa;
}

.sources-section {
  margin-top: 20px;
  border-top: 1px solid #e8e8e8;
  padding-top: 10px;
}

.source-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 5px;
  cursor: pointer;
}

.source-item:hover {
  background-color: #e8e8e8;
}

.source-icon {
  margin-right: 10px;
}

.source-info {
  flex-grow: 1;
}

/* Agent特定样式 */
.agent-thinking {
  font-style: italic;
  color: #666;
}

.tool-call {
  background-color: #f0f8ff;
  border-left: 3px solid #1e90ff;
  padding: 8px 12px;
  margin: 10px 0;
  font-family: monospace;
  font-size: 0.9em;
}

.tool-result {
  background-color: #f0fff0;
  border-left: 3px solid #32cd32;
  padding: 8px 12px;
  margin: 10px 0;
  font-family: monospace;
  font-size: 0.9em;
}

code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 5px;
  overflow-x: auto;
}